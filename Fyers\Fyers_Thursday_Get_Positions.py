from datetime import datetime
from fyers_apiv3 import fyersModel
import time
from Fyers import Fyers_Tradebook 
from logger_config import get_logger, setup_logger

#Get logger
logger = setup_logger('main_logger', 'main.log')
logger = get_logger('main_logger')

def getvalidpositions(fyers, strategy_name=None):
    try:
        positions_data = fyers.positions()
        trades_data = fyers.tradebook()

        if positions_data is None or "netPositions" not in positions_data:
            return {"error": "Failed to retrieve positions data or unexpected format."}

        if trades_data is None or "tradeBook" not in trades_data:
            return {"error": "Failed to retrieve trades data or unexpected format."}

        valid_tokens = []

        tradebook = Fyers_Tradebook.get_tradebook()
        print("Tradebook: ", tradebook)
        updateflag = False
        for trade in trades_data["tradeBook"]:
            if trade not in tradebook:
               tradebook.append(trade)
               updateflag = True    

        if(updateflag):
            Fyers_Tradebook.update_tradebook(tradebook)    

        if len(tradebook)>0:
            for trade in tradebook:
                order_id = trade["orderNumber"]
                token = trade["fyToken"]
                strategy_tag = trade['orderTag']  # Find strategy tag
                #Store tokens which are part of a particular strategy
                if strategy_name in strategy_tag:
                    valid_tokens.append(token)

        strategy_wise_positions = []
        if positions_data:
            for position in positions_data["netPositions"]:
                token = position["fyToken"]
                netqty = position["netQty"]
                
                if(token in valid_tokens):
                    strategy_wise_positions.append(position)    

        return strategy_wise_positions
    except Exception as e:
        return {"error": str(e)}

def thursday_get_positions(fyers, strategy_name=None):
    """
    Fetch all open positions from Fyers API and segregate them into CE, PE as well as Buy and Short side.
    :return: Dictionary containing segregated positions with only symbol, ltp, and side.
    """
    try:
        if(strategy_name):
            positions = getvalidpositions(fyers, strategy_name)
            
        else:
            response = fyers.positions()
            if response.get("s") == "ok":
                positions = response.get("netPositions", [])
                
        # Check if the response is valid
        if positions:

            # Segregation dictionaries
            ce_buy = []
            ce_short = {}
            pe_buy = []
            pe_short = {}

            for position in positions:
                # Assuming 'quantity' is a key that indicates if the position is open
                if position.get("qty", 0) > 0 and position.get("productType") == "MARGIN" and "NIFTY" in position.get("symbol") and "BANKNIFTY" not in position.get("symbol"):

                    symbol = position["symbol"]
                    ltp = position["ltp"]
                    side = "BUY" if position["side"] ==1 else "SHORT"
                    qty = position["qty"]

                    # Classify as CE or PE and Buy or Short
                    if "CE" in symbol:
                        if side == "BUY":
                            ce_buy.append({"symbol": symbol, "ltp": ltp, "side": side, "qty": qty})
                        else:
                            ce_short = {"symbol": symbol, "ltp": ltp, "side": side, "qty": qty}
                    elif "PE" in symbol:
                        if side == "BUY":
                            pe_buy.append({"symbol": symbol, "ltp": ltp, "side": side, "qty": qty})
                        else:
                            pe_short = {"symbol": symbol, "ltp": ltp, "side": side, "qty": qty}
            
            # Check for at least 4 buy positions
            total_buy_positions = len(ce_buy) + len(pe_buy)
            if total_buy_positions < 4:
                logger.warning(f"Not enough buy positions. Found {total_buy_positions}, but at least 4 required.")          
            
            #Print the segregated positions
            logger.info(f"CE_BUY:{ce_buy}")
            logger.info(f"CE_SHORT:{ce_short}")
            logger.info(f"PE_BUY:{pe_buy}")
            logger.info(f"PE_SHORT:{pe_short}")

            return {
                "CE_BUY": ce_buy,
                "CE_SHORT": ce_short,
                "PE_BUY": pe_buy,
                "PE_SHORT": pe_short,

            }
    except Exception as e:
        return {"error": str(e)}
#---------------------------------------------------------------------------------------

#CLIENT_ID =  "O5MRPDE4TQ-100" 

#ACCESS_TOKEN='eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJhcGkuZnllcnMuaW4iLCJpYXQiOjE3NDAwMzIzOTYsImV4cCI6MTc0MDA5Nzg1NiwibmJmIjoxNzQwMDMyMzk2LCJhdWQiOlsieDowIiwieDoxIiwieDoyIiwiZDoxIiwiZDoyIiwieDoxIiwieDowIiwieDoxIiwieDowIl0sInN1YiI6ImFjY2Vzc190b2tlbiIsImF0X2hhc2giOiJnQUFBQUFCbnRzbU1LOEpHc0V4Y1FPZTNwd2NBOVBRYkUzcmVnbTgzbEo0Q1lCTU5vRGJ1eGk3eG00bUFwalBRdlFKQ3JoNVh6aTJ1NW81NWlqRDNraTRlUEN0OHFqSERJazF1Mk1ZVVJWSlhKY28xMzB5clBHdz0iLCJkaXNwbGF5X25hbWUiOiJZQVRJTiBLVU1BUiBCSEFUSUEiLCJvbXMiOiJLMSIsImhzbV9rZXkiOiI0NWZkNmEwZTYxZTA5NzhiMjM5ZmFkMDJlMGY1MjdmZDhhOWE0NTY3MGM2YmZhNjIzZjE5N2RlYyIsImlzRGRwaUVuYWJsZWQiOiJOIiwiaXNNdGZFbmFibGVkIjoiTiIsImZ5X2lkIjoiWVkwMzQ0MCIsImFwcFR5cGUiOjEwMCwicG9hX2ZsYWciOiJOIn0.6VKGnL9WmBwQkfy-2TgPeKWjbpQBUChmknzCUEq8w54'

#fyers = fyersModel.FyersModel(client_id=CLIENT_ID, token=ACCESS_TOKEN, is_async=False,log_path="")

#Example Usage    
#positions = thursday_get_positions(fyers)
#print(positions)