from dotenv import load_dotenv
import os, time
from Symphony import Symphony_To_Fyers_Converter 
from Fyers import Fyers_Utility 
from Symphony import Symphony_Utility

# Load environment variables from .env file
load_dotenv()

# Function to fetch total P&L for both open and closed positions
def symphony_get_total_pnl(broker, clientID):
    response = broker.interactive_symphony.get_position_netwise(clientID)

    total_pnl = 0.0
    open_pnl = 0.0
    closed_pnl = 0.0

    if response.get("type") == "success":
        positions = response.get("result", {}).get("positionList", [])
        symbols = []
        symbol_position_map = {}

        # First pass: collect relevant symbols
        for position in positions:
            if position.get("ProductType", "") == "NRML" and \
               "NIFTY" in position.get("TradingSymbol", "") and \
               "BANKNIFTY" not in position.get("TradingSymbol", "") and \
               "CNC" not in position.get("TradingSymbol", ""):

                exchange_id = int(position.get("ExchangeInstrumentId"))
                fyers_symbol = Symphony_To_Fyers_Converter.convert_to_fyers_symbol(exchange_id)
                symbols.append(fyers_symbol)
                symbol_position_map[fyers_symbol] = position

       
        ltps = Fyers_Utility.get_ltp_batch(broker.fyers, symbols)
       

        # Second pass: calculate P&L
        for symbol, position in symbol_position_map.items():
            ltp = ltps.get(symbol)
            if ltp is None:
                continue  # skip if LTP not found

            buy_avg_price = float(position.get("BuyAveragePrice", 0))
            sell_avg_price = float(position.get("SellAveragePrice", 0))
            qty = float(position.get("Quantity", 0))
            NetAmount = float(position.get("NetAmount", 0))

            if qty == 0:
                closed_pnl += NetAmount
            elif qty > 0:
                open_pnl += (ltp - buy_avg_price) * qty  # Long
            else:
                open_pnl += (sell_avg_price - ltp) * abs(qty)  # Short

        total_pnl = round(open_pnl + closed_pnl, 2)
        open_pnl = round(open_pnl, 2)
        closed_pnl = round(closed_pnl, 2)

    return total_pnl, open_pnl, closed_pnl


#------------------------------------------------------------------------------------------------------------
#broker = BrokerContext() 
#symphony_get_total_pnl(broker, clientID = os.getenv("CLIENT_ID"))
