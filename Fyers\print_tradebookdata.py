import pickle
import pandas as pd

def print_pickle_data(file_path):
    # Open the pickle file in binary read mode
    with open(file_path, 'rb') as file:
        # Load the data from the pickle file
        data = pickle.load(file)
        print(data)
        df = pd.DataFrame(data, columns=['symbol', 'orderDateTime', 'side'])

        # Print the data
        print(df)

# Example usage
print_pickle_data('tradebook.pickle')