from Symphony import Connect 
import logger_config 
from dotenv import load_dotenv
import os


load_dotenv()

#Get logger
logger = logger_config.get_logger('main_logger')

def initialize_market_symphony():

    #XTS-Symphony Market API CREDENTIALS
    API_KEY = os.getenv('Market_API_KEY')
    API_SECRET = os.getenv('Market_API_SECRET')
    clientID = os.getenv('Market_clientID')
    XTS_API_BASE_URL = os.getenv('Market_XTS_API_BASE_URL')
    source = os.getenv('Market_source')

    market_symphony = Connect.XTSConnect(API_KEY, API_SECRET, source)
   
    response2 = market_symphony.marketdata_login()
    #print("\nMarket Login Response:", response2)

    return market_symphony

def initialize_interactive_symphony():
    #XTS-Symphony Interactive API CREDENTIALS
    API_KEY = os.getenv('Interactive_API_KEY')
    API_SECRET = os.getenv('Interactive_API_SECRET')
    clientID = os.getenv('Interactive_clientID')
    XTS_API_BASE_URL = os.getenv('Interactive_XTS_API_BASE_URL')
    source = os.getenv('Interactive_source')

    interactive_symphony = Connect.XTSConnect(API_KEY, API_SECRET, source)
    
    response1 = interactive_symphony.interactive_login()
    #print("\nInteractive Login Response:", response1)
     
    return interactive_symphony
#---------------------------------------------------------------------------

#market_symphony = initialize_market_symphony()
#interactive_symphony = initialize_interactive_symphony()



