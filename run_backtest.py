#!/usr/bin/env python3
"""
Simple script to run the BCS strategy backtesting
"""

import os
import sys
from datetime import datetime
from pathlib import Path

# Add the current directory to Python path
sys.path.append(str(Path(__file__).parent))

from backtesting import BacktestConfig, BacktestRunner


def main():
    """Main function to run backtesting"""
    
    print("BCS Strategy Backtesting Framework")
    print("=" * 50)
    
    # Create configuration
    config = BacktestConfig()
    
    # You can customize these settings
    config.data_root_path = "Parquet_Files"
    config.data_subfolder = "Thursday_output_folder"
    config.initial_capital = 1000000.0  # 10 Lakh
    config.global_qty = 50
    config.commission_per_trade = 20.0
    config.slippage_bps = 5.0
    
    # Set date range (optional - if not set, will process all available dates)
    # config.start_date = "20210107"  # Format: YYYYMMDD
    # config.end_date = "20210131"    # Format: YYYYMMDD
    
    # Risk management settings
    config.max_loss_per_day = 50000.0   # Max loss per day
    config.max_profit_per_day = 100000.0 # Max profit per day
    
    # Output settings
    config.results_dir = "backtesting_results"
    config.save_trades = True
    config.save_daily_pnl = True
    config.generate_plots = True
    
    print(f"Configuration:")
    print(f"  Data Path: {config.data_root_path}/{config.data_subfolder}")
    print(f"  Initial Capital: ${config.initial_capital:,.2f}")
    print(f"  Quantity per trade: {config.global_qty}")
    print(f"  Results Directory: {config.results_dir}")
    print()
    
    # Check if data directory exists
    data_path = Path(config.data_root_path) / config.data_subfolder
    if not data_path.exists():
        print(f"ERROR: Data directory not found: {data_path}")
        print("Please ensure your parquet data is in the correct directory structure:")
        print("  Parquet_Files/")
        print("    Thursday_output_folder/")
        print("      20210107/")
        print("        CE_BUY/")
        print("        CE_SELL/")
        print("        PE_BUY/")
        print("        PE_SELL/")
        return
    
    # Create and run backtester
    try:
        runner = BacktestRunner(config)
        
        # Option 1: Run full backtest
        print("Starting full backtesting process...")
        results = runner.run_backtest()
        
        # Option 2: Run quick test (uncomment to use instead)
        # print("Running quick test...")
        # results = runner.run_quick_test("20210107")  # Test specific date
        
        if results:
            print("\n" + "=" * 50)
            print("BACKTESTING COMPLETED SUCCESSFULLY!")
            print("=" * 50)
            
            # Print key results
            perf = results.get('performance_metrics', {})
            print(f"Total Return: {perf.get('total_return_pct', 0):.2f}%")
            print(f"Total PnL: ${perf.get('total_pnl', 0):,.2f}")
            print(f"Max Drawdown: {perf.get('max_drawdown_pct', 0):.2f}%")
            print(f"Win Rate: {perf.get('win_rate_pct', 0):.2f}%")
            print(f"Total Trades: {perf.get('total_trades', 0)}")
            print(f"Sharpe Ratio: {perf.get('sharpe_ratio', 0):.2f}")
            
            print(f"\nDetailed results saved to: {config.results_dir}/")
            
        else:
            print("BACKTESTING FAILED!")
            print("Check the logs for error details.")
            
    except KeyboardInterrupt:
        print("\nBacktesting interrupted by user.")
    except Exception as e:
        print(f"ERROR: {e}")
        print("Check the backtesting.log file for detailed error information.")


if __name__ == "__main__":
    main()
