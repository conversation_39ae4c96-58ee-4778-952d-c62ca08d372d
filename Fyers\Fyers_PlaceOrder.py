from fyers_apiv3 import fyersModel
from Fyers import Fyers_Utility 
import logger_config 
import time

#Get logger
#logger = logger_config.setup_logger('main_logger', 'main.log')
logger = logger_config.get_logger('main_logger')


def adjusted_limit_price(price, side):
    logger.info(f"price and side, {price}, {side}")
    if price <= 2:
        logger.info(f"price is less than 2")
        if side == "buy":
            price += 0.10
        else:
            price -= 0.10
        
        # Find the nearest 0.05 multiple
        limitAdjustedPrice = round(price / 0.05) * 0.05
        limitAdjustedPrice = max(0.05,limitAdjustedPrice)
        logger.info(f"limitAdjustedPrice:, {limitAdjustedPrice:.2f}")
        return round(limitAdjustedPrice, 2)
    else:
        logger.info(f"price is greater than or equal to 2")
        if side == "buy":
            buffer = 1.04
        else:
            buffer = 0.96
        
        # Apply buffer and find the nearest 0.05 multiple
        limitAdjustedPrice = round((price * buffer) / 0.05) * 0.05
        limitAdjustedPrice = max(0.05,limitAdjustedPrice)
        logger.info(f"limitAdjustedPrice:, {limitAdjustedPrice:.2f}")

    # Ensure the result is rounded to two decimal places for clarity
    return round(limitAdjustedPrice, 2)

def fyers_place_order(broker, option, side, qty, order_type):
    """
    Places an order based on provided parameters.
    :param option: Dictionary containing symbol and price.
    :param side: "buy" for 1, "sell" for -1.
    :param qty: Quantity to trade.
    :param order_type: "market" for 2, "limit" for 1.
    :param vix: Current VIX value for price adjustment if needed.
    """
    fyers = broker.fyers

    loop = 0
    orderplaced = False
    while (orderplaced == False and loop < 5):
        loop += 1
        try:
            option_ltp = Fyers_Utility.get_ltp_from_symbol(fyers, option["symbol"])
            limitAdjustedPrice = adjusted_limit_price(option_ltp,side) if order_type == "limit" else 0
            order_payload = {
                "symbol": option["symbol"],
                "side": 1 if side == "buy" else -1,
                "qty": qty,
                "type": 1 if order_type == "limit" else 2,
                "productType": "MARGIN",
                "limitPrice": limitAdjustedPrice,
                "stopPrice": 0,
                "validity": "IOC",
                "orderTag": "BCS"
            }
            logger.debug(f"Order Payload: {order_payload}")  # Log the payload
            #print("Order Payload: ", order_payload)
            response = fyers.place_order(order_payload)
            #print(response)
            logger.debug(f"Order Response: {response}")  # Log the response

            if response.get("s") == "ok":
                orderplaced=True
                logger.info(f"Order placed successfully: Order ID {response['id']}")
            else:
                logger.error(f"Order placement failed: {response.get('message', 'Unknown error')}")
                if 'data' not in response:
                    logger.error("Response missing 'data' field.")    
        except KeyError as e:
            logger.error(f"KeyError placing order: {e} | Response: {response}")
        except AssertionError as e:
            logger.error(f"Validation error: {e}")
        except Exception as e:
            logger.error(f"Unexpected error placing order: {e}")    
            continue
        time.sleep(1)

    return response

