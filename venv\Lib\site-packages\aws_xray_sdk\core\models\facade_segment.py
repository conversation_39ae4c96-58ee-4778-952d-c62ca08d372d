from .segment import Segment
from ..exceptions.exceptions import FacadeSegmentMutationException


MUTATION_UNSUPPORTED_MESSAGE = 'FacadeSegments cannot be mutated.'


class FacadeSegment(Segment):
    """
    This type of segment should only be used in an AWS Lambda environment.
    It holds the same id, traceid and sampling decision as
    the segment generated by Lambda service but its properties cannot
    be mutated except for its subsegments. If this segment is created
    before Lambda worker finishes initializatioin, all the child
    subsegments will be discarded.
    """
    def __init__(self, name, entityid, traceid, sampled):

        self.initializing = self._is_initializing(
            entityid=entityid,
            traceid=traceid,
            sampled=sampled,
        )

        super().__init__(
            name=name,
            entityid=entityid,
            traceid=traceid,
            sampled=sampled,
        )

    def close(self, end_time=None):
        """
        Unsupported operation. Will raise an exception.
        """
        raise FacadeSegmentMutationException(MUTATION_UNSUPPORTED_MESSAGE)

    def put_http_meta(self, key, value):
        """
        Unsupported operation. Will raise an exception.
        """
        raise FacadeSegmentMutationException(MUTATION_UNSUPPORTED_MESSAGE)

    def put_annotation(self, key, value):
        """
        Unsupported operation. Will raise an exception.
        """
        raise FacadeSegmentMutationException(MUTATION_UNSUPPORTED_MESSAGE)

    def put_metadata(self, key, value, namespace='default'):
        """
        Unsupported operation. Will raise an exception.
        """
        raise FacadeSegmentMutationException(MUTATION_UNSUPPORTED_MESSAGE)

    def set_aws(self, aws_meta):
        """
        Unsupported operation. Will raise an exception.
        """
        raise FacadeSegmentMutationException(MUTATION_UNSUPPORTED_MESSAGE)

    def set_user(self, user):
        """
        Unsupported operation. Will raise an exception.
        """
        raise FacadeSegmentMutationException(MUTATION_UNSUPPORTED_MESSAGE)

    def add_throttle_flag(self):
        """
        Unsupported operation. Will raise an exception.
        """
        raise FacadeSegmentMutationException(MUTATION_UNSUPPORTED_MESSAGE)

    def add_fault_flag(self):
        """
        Unsupported operation. Will raise an exception.
        """
        raise FacadeSegmentMutationException(MUTATION_UNSUPPORTED_MESSAGE)

    def add_error_flag(self):
        """
        Unsupported operation. Will raise an exception.
        """
        raise FacadeSegmentMutationException(MUTATION_UNSUPPORTED_MESSAGE)

    def add_exception(self, exception, stack, remote=False):
        """
        Unsupported operation. Will raise an exception.
        """
        raise FacadeSegmentMutationException(MUTATION_UNSUPPORTED_MESSAGE)

    def apply_status_code(self, status_code):
        """
        Unsupported operation. Will raise an exception.
        """
        raise FacadeSegmentMutationException(MUTATION_UNSUPPORTED_MESSAGE)

    def serialize(self):
        """
        Unsupported operation. Will raise an exception.
        """
        raise FacadeSegmentMutationException(MUTATION_UNSUPPORTED_MESSAGE)

    def ready_to_send(self):
        """
        Facade segment should never be sent out. This always
        return False.
        """
        return False

    def increment(self):
        """
        Increment total subsegments counter by 1.
        """
        self._subsegments_counter.increment()

    def decrement_ref_counter(self):
        """
        No-op
        """
        pass

    def _is_initializing(self, entityid, traceid, sampled):
        return not entityid or not traceid or sampled is None
