import os
from dotenv import load_dotenv
import pandas as pd
import logger_config
from Symphony import Symphony_To_Fyers_Converter 
from Symphony import Symphony_Get_Masterdf
from Fyers import Fyers_Utility
from Symphony import Symphony_Utility
import config

# Load environment variables from .env file
load_dotenv()
logger = logger_config.get_logger('main_logger')
broker = config.BrokerContext()

def get_positions_xts(broker, clientID):
    """
    Fetch all open positions from Symphony XTS API and segregate them into CE, PE as well as Buy and Short side.
    Uses batch LTP fetch for improved performance.
    :return: Dictionary containing segregated positions with only symbol, ltp, and side.
    """
    try:
        # Force reinitialize Symphony connection
        broker.interactive_symphony = Symphony_Utility.initialize_interactive_symphony()

        # Fetch positions using Symphony XTS API
        response = broker.interactive_symphony.get_position_netwise(clientID)

        if response.get("type") == "success":
            positions = response.get("result", {}).get("positionList", [])

            ce_buy = {}
            ce_short = {}
            pe_buy = {}
            pe_short = {}

            fyers_symbols_map = {}
            filtered_positions = []

            # First pass: filter and prepare symbols for batch LTP fetch
            for position in positions:
                if int(position.get("Quantity", 0)) != 0 and position.get("ProductType", "") == "NRML" \
                        and "NIFTY" in position.get("TradingSymbol", "") and "BANKNIFTY" not in position.get("TradingSymbol", "") \
                        and "CNC" not in position.get("TradingSymbol", ""):

                    fyers_symbol = Symphony_To_Fyers_Converter.convert_to_fyers_symbol(int(position.get("ExchangeInstrumentId")))
                    fyers_symbols_map[fyers_symbol] = position  # Map symbol to position for later reference
                    filtered_positions.append((position, fyers_symbol))

            # Batch LTP fetch
            ltp_data = Fyers_Utility.get_ltp_batch(broker.fyers, list(fyers_symbols_map.keys()))

            # Second pass: assign LTP and classify
            for position, fyers_symbol in filtered_positions:
                symbol = position.get("TradingSymbol", "")
                qty = int(position.get("Quantity", 0))
                side = "BUY" if qty > 0 else "SHORT"
                ltp = ltp_data.get(fyers_symbol, 0.0)  # default to 0.0 if missing

                if "CE" in symbol:
                    if side == "BUY":
                        ce_buy = {"symbol": fyers_symbol, "ltp": ltp, "side": side, "qty": abs(qty)}
                    else:
                        ce_short = {"symbol": fyers_symbol, "ltp": ltp, "side": side, "qty": abs(qty)}
                elif "PE" in symbol:
                    if side == "BUY":
                        pe_buy = {"symbol": fyers_symbol, "ltp": ltp, "side": side, "qty": abs(qty)}
                    else:
                        pe_short = {"symbol": fyers_symbol, "ltp": ltp, "side": side, "qty": abs(qty)}

            # Log and return results
            logger.info(f"CE_BUY: {ce_buy}")
            logger.info(f"CE_SHORT: {ce_short}")
            logger.info(f"PE_BUY: {pe_buy}")
            logger.info(f"PE_SHORT: {pe_short}")

            return {
                "CE_BUY": ce_buy,
                "CE_SHORT": ce_short,
                "PE_BUY": pe_buy,
                "PE_SHORT": pe_short
            }
        else:
            return {"error": response.get("description", "Failed to fetch positions")}

    except Exception as e:
        return {"error": str(e)}
