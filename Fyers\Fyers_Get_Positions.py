from datetime import datetime
from fyers_apiv3 import fyersModel
import time
from Fyers import Fyers_Tradebook
import logger_config 

#Get logger
#logger = logger_config.setup_logger('main_logger', 'main.log')
logger = logger_config.get_logger('main_logger')

def getvalidpositions(fyers, strategy_name=None):
    try:
        positions_data = fyers.positions()
        trades_data = fyers.tradebook()

        if positions_data is None or "netPositions" not in positions_data:
            return {"error": "Failed to retrieve positions data or unexpected format."}

        if trades_data is None or "tradeBook" not in trades_data:
            return {"error": "Failed to retrieve trades data or unexpected format."}

        valid_tokens = []

        tradebook = Fyers_Tradebook.get_tradebook()
        #print("Tradebook: ", tradebook)
        updateflag = False
        for trade in trades_data["tradeBook"]:
            if trade not in tradebook:
                tradebook.append(trade)
                updateflag = True    

        if(updateflag):
            Fyers_Tradebook.update_tradebook(tradebook)    

        if len(tradebook)>0:
            for trade in tradebook:
                order_id = trade["orderNumber"]
                token = trade["fyToken"]
                strategy_tag = trade['orderTag']  # Find strategy tag
                # Store tokens which are part of a particular strategy
                if strategy_name in strategy_tag:
                    valid_tokens.append(token)

        strategy_wise_positions = []
        if positions_data:
            for position in positions_data["netPositions"]:
                token = position["fyToken"]
                netqty = position["netQty"]
                
                if(token in valid_tokens):
                    strategy_wise_positions.append(position)    

        return strategy_wise_positions
    except Exception as e:
        return {"error": str(e)}

def get_positions(fyers, strategy_name=None):
    """
    Fetch all open positions from Fyers API and segregate them into CE, PE as well as Buy and Short side.
    :return: Dictionary containing segregated positions with only symbol, ltp, and side.
    """
    try:
        if(strategy_name):
            positions = getvalidpositions(fyers, strategy_name)
            
        else:
            response = fyers.positions()
            if response.get("s") == "ok":
                positions = response.get("netPositions", [])
        # Check if the response is valid
        if positions:

            # Segregation dictionaries
            ce_buy = {}
            ce_short = {}
            pe_buy = {}
            pe_short = {}

            for position in positions:

                # Assuming 'quantity' is a key that indicates if the position is open
                if position.get("qty", 0) > 0 and position.get("productType") == "MARGIN" and "NIFTY" in position.get("symbol") and "BANKNIFTY" not in position.get("symbol") and "CNC" not in position.get("symbol"):

                    symbol = position["symbol"]
                    ltp = position["ltp"]
                    side = "BUY" if position["side"] ==1 else "SHORT"
                    qty = position["qty"]
                    PNL ='tatal_pnl'

                    # Classify as CE or PE and Buy or Short
                    if "CE" in symbol:
                        if side == "BUY":
                            ce_buy = {"symbol": symbol, "ltp": ltp, "side": side, "qty": qty}
                        else:
                            ce_short = {"symbol": symbol, "ltp": ltp, "side": side, "qty": qty}
                    elif "PE" in symbol:
                        if side == "BUY":
                            pe_buy = {"symbol": symbol, "ltp": ltp, "side": side, "qty": qty}
                        else:
                            pe_short = {"symbol": symbol, "ltp": ltp, "side": side, "qty": qty}

              # Print the segregated positions
            logger.info(f"CE_BUY:{ce_buy}")
            logger.info(f"CE_SHORT:{ce_short}")
            logger.info(f"PE_BUY:{pe_buy}")
            logger.info(f"PE_SHORT:{pe_short}")

            return {
                "CE_BUY": ce_buy,
                "CE_SHORT": ce_short,
                "PE_BUY": pe_buy,
                "PE_SHORT": pe_short
            }
    except Exception as e:
        return {"error": str(e)}
#---------------------------------------------------------------------------------------

#fyers = initialize_fyers()

#Example Usage    
#positions = get_positions(fyers)