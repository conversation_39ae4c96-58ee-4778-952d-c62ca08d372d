# BCS Strategy Backtesting Framework

A comprehensive backtesting framework for the Balanced Calendar Spread (BCS) trading strategy, designed to work with historical parquet data.

## Overview

This backtesting framework simulates the execution of your live BCS trading strategy using historical market data. It includes:

- **Data Loading**: Processes parquet files from your specified directory structure
- **Market Simulation**: Simulates real-time market conditions and order execution
- **Strategy Implementation**: Replicates your BCS strategy logic including delta neutrality checks
- **Portfolio Management**: Tracks positions, PnL, and risk metrics
- **Results Analysis**: Comprehensive performance analysis and reporting

## Directory Structure

```
backtesting/
├── __init__.py              # Package initialization
├── config.py                # Configuration management
├── data_loader.py           # Historical data loading
├── market_simulator.py      # Market data simulation
├── portfolio_manager.py     # Position and PnL tracking
├── strategy_backtester.py   # BCS strategy implementation
├── results_analyzer.py      # Performance analysis
└── main_runner.py          # Main backtesting orchestrator

run_backtest.py             # Simple script to run backtesting
README_BACKTESTING.md       # This file
```

## Data Requirements

Your historical data should be organized as follows:

```
Parquet_Files/
└── Thursday_output_folder/
    ├── 20210107/
    │   ├── CE_BUY/
    │   │   └── *.parquet
    │   ├── CE_SELL/
    │   │   └── *.parquet
    │   ├── PE_BUY/
    │   │   └── *.parquet
    │   └── PE_SELL/
    │       └── *.parquet
    ├── 20210108/
    │   └── ... (same structure)
    └── ... (more dates)
```

### Expected Data Format

Each parquet file should contain columns like:
- `timestamp`: DateTime of the data point
- `symbol`: Option symbol (e.g., "NSE:NIFTY21JAN23500CE")
- `ltp`: Last traded price
- `bid`: Bid price (optional)
- `ask`: Ask price (optional)
- `volume`: Trading volume (optional)
- `oi`: Open interest (optional)

## Quick Start

1. **Install Dependencies**:
   ```bash
   pip install pandas numpy matplotlib seaborn
   ```

2. **Prepare Your Data**:
   - Ensure your parquet files are in the correct directory structure
   - Verify the data format matches the expected schema

3. **Run Backtesting**:
   ```bash
   python run_backtest.py
   ```

## Configuration

### Basic Configuration

Edit the `run_backtest.py` file to customize settings:

```python
config = BacktestConfig()
config.data_root_path = "Parquet_Files"
config.initial_capital = 1000000.0  # 10 Lakh
config.global_qty = 50
config.commission_per_trade = 20.0
config.slippage_bps = 5.0
```

### Advanced Configuration

For more detailed configuration, modify the `BacktestConfig` class in `backtesting/config.py`:

```python
# Trading parameters
config.start_time = time(9, 15)      # Market start time
config.exit_time = time(15, 30)      # Market exit time
config.delta_check_interval = 15     # Delta check interval in seconds

# Risk management
config.max_loss_per_day = 50000.0    # Maximum loss per day
config.max_profit_per_day = 100000.0 # Maximum profit per day

# Date range (optional)
config.start_date = "20210107"       # Start date (YYYYMMDD)
config.end_date = "20210131"         # End date (YYYYMMDD)
```

## Strategy Logic

The backtesting framework implements the same BCS strategy logic as your live system:

### 1. Strategy Initialization
- Fetches VIX value for the day
- Calculates sell option price based on VIX
- Determines calendar spread price (80% of sell price)
- Applies resize factors based on current weekday
- Places initial BCS orders (CE/PE buy and sell)

### 2. Delta Neutrality Monitoring
- Checks delta neutrality every 15 seconds (configurable)
- Calculates ratio of lower LTP to higher LTP for short positions
- Compares against VIX-based threshold
- Triggers delta adjustment when threshold is breached

### 3. Delta Adjustment Actions
- **ATM/ITM Condition**: Squares off all positions and recreates strategy
- **Delta Adjustment**: Closes profitable leg and establishes new positions

### 4. Exit Conditions
- Time-based exit (market close)
- Risk-based exit (max loss/profit limits)
- Drawdown limits

## Output and Results

### Generated Files

The backtesting framework generates several output files:

1. **Results Summary** (`backtest_results_YYYYMMDD_HHMMSS.json`):
   - Performance metrics
   - Trade analysis
   - Risk metrics
   - Strategy-specific analysis

2. **Trade Details** (`trades_YYYYMMDD_HHMMSS.csv`):
   - Individual trade records
   - Entry/exit prices and times
   - PnL for each trade

3. **Daily PnL** (`daily_pnl_YYYYMMDD_HHMMSS.csv`):
   - Daily performance summary
   - Cumulative returns
   - Risk metrics

4. **Portfolio History** (`portfolio_history_YYYYMMDD_HHMMSS.csv`):
   - Portfolio value over time
   - Position details
   - Unrealized PnL

### Visualization

If `generate_plots = True`, the framework creates:
- Portfolio value over time
- Daily PnL distribution
- Trade PnL scatter plot

### Key Metrics

The framework calculates comprehensive performance metrics:

- **Return Metrics**: Total return, annualized return
- **Risk Metrics**: Maximum drawdown, Sharpe ratio, Sortino ratio
- **Trade Metrics**: Win rate, average win/loss, profit factor
- **Strategy Metrics**: Delta adjustments, strategy recreations

## Customization

### Adding Custom Indicators

To add custom indicators or modify strategy logic:

1. Edit `strategy_backtester.py`
2. Modify the `process_market_update()` method
3. Add new conditions in `check_exit_conditions()`

### Custom Data Sources

To use different data sources:

1. Modify `data_loader.py`
2. Update the `load_option_data()` method
3. Ensure data format consistency

### Risk Management

To add custom risk rules:

1. Edit `portfolio_manager.py`
2. Modify `check_risk_limits()` method
3. Add new risk conditions

## Troubleshooting

### Common Issues

1. **Data Not Found**:
   - Verify directory structure matches expected format
   - Check file permissions
   - Ensure parquet files are not corrupted

2. **Memory Issues**:
   - Reduce date range for testing
   - Enable data cache clearing
   - Process data in smaller chunks

3. **Performance Issues**:
   - Increase delta check interval
   - Reduce logging verbosity
   - Use SSD storage for data files

### Debug Mode

For debugging, use the quick test feature:

```python
runner = BacktestRunner(config)
results = runner.run_quick_test("20210107")  # Test single date
```

## Performance Optimization

### Tips for Better Performance

1. **Data Optimization**:
   - Use compressed parquet files
   - Pre-filter unnecessary columns
   - Store data on fast storage (SSD)

2. **Memory Management**:
   - Clear data cache periodically
   - Process data in chunks for large datasets
   - Use appropriate data types

3. **Parallel Processing**:
   - Run multiple date ranges in parallel
   - Use multiprocessing for independent days

## Support and Maintenance

### Logging

All activities are logged to `backtesting.log`. Check this file for:
- Detailed execution flow
- Error messages and stack traces
- Performance metrics

### Validation

Before running on large datasets:
1. Test with a single day using `run_quick_test()`
2. Verify results match expected behavior
3. Check data quality and completeness

### Updates

To update the framework:
1. Backup your configuration
2. Update strategy logic as needed
3. Test with known data before production runs

## Example Usage

```python
from backtesting import BacktestConfig, BacktestRunner

# Create configuration
config = BacktestConfig()
config.start_date = "20210107"
config.end_date = "20210131"
config.initial_capital = 1000000.0

# Run backtest
runner = BacktestRunner(config)
results = runner.run_backtest()

# Print results
runner.results_analyzer.print_summary_report(results)
```

This framework provides a solid foundation for backtesting your BCS strategy. Customize it according to your specific requirements and data format.
