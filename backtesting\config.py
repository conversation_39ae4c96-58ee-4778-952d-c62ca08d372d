"""
Backtesting Configuration Module
Manages all configuration settings for the backtesting framework
"""

import os
from datetime import datetime, time
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Tuple
from pathlib import Path


@dataclass
class BacktestConfig:
    """Configuration class for backtesting parameters"""
    
    # Data paths
    data_root_path: str = "Parquet_Files"
    data_subfolder: str = "Thursday_output_folder"
    
    # Strategy parameters
    global_qty: int = 50
    vix_price_map: Dict[int, int] = field(default_factory=lambda: {
        9: 14, 10: 15, 11: 16, 12: 17, 13: 18, 14: 19, 15: 20, 16: 20,
        17: 21, 18: 21, 19: 22, 20: 22, 21: 23
    })
    
    # Trading times
    start_time: time = time(9, 15)  # 9:15 AM
    exit_time: time = time(15, 30)  # 3:30 PM
    
    # Delta check parameters
    delta_check_interval: int = 15  # seconds
    vix_threshold_diff: float = 0.0  # Diff parameter from env
    
    # Expiry settings
    expiry_day: str = "THURSDAY"
    
    # Backtesting specific parameters
    start_date: Optional[str] = None  # Format: YYYYMMDD
    end_date: Optional[str] = None    # Format: YYYYMMDD
    initial_capital: float = 1000000.0  # 10 Lakh
    commission_per_trade: float = 20.0
    slippage_bps: float = 5.0  # 5 basis points
    
    # Risk management
    max_loss_per_day: float = 50000.0  # Max loss per day
    max_profit_per_day: float = 100000.0  # Max profit per day
    
    # Resize values for different days (from New_Recreate_Strategy.py)
    resize_values: Dict[str, Dict[str, float]] = field(default_factory=lambda: {
        "THURSDAY": {
            "MONDAY": 1.0,
            "TUESDAY": 1.0, 
            "WEDNESDAY": 1.0,
            "THURSDAY": 1.0,
            "FRIDAY": 1.0
        }
    })
    
    # Output settings
    results_dir: str = "backtesting_results"
    save_trades: bool = True
    save_daily_pnl: bool = True
    generate_plots: bool = True
    
    def __post_init__(self):
        """Post initialization validation and setup"""
        # Create results directory if it doesn't exist
        Path(self.results_dir).mkdir(parents=True, exist_ok=True)
        
        # Validate dates if provided
        if self.start_date:
            try:
                datetime.strptime(self.start_date, "%Y%m%d")
            except ValueError:
                raise ValueError(f"Invalid start_date format: {self.start_date}. Use YYYYMMDD")
                
        if self.end_date:
            try:
                datetime.strptime(self.end_date, "%Y%m%d")
            except ValueError:
                raise ValueError(f"Invalid end_date format: {self.end_date}. Use YYYYMMDD")
    
    @classmethod
    def from_env(cls) -> 'BacktestConfig':
        """Create configuration from environment variables"""
        config = cls()
        
        # Override with environment variables if they exist
        if os.getenv("GLOBAL_QTY"):
            config.global_qty = int(os.getenv("GLOBAL_QTY"))
            
        if os.getenv("Diff"):
            config.vix_threshold_diff = float(os.getenv("Diff"))
            
        if os.getenv("Expiry_Day"):
            config.expiry_day = os.getenv("Expiry_Day").upper()
            
        return config
    
    def get_data_path(self, date_str: str) -> Path:
        """Get the full path to data for a specific date"""
        return Path(self.data_root_path) / self.data_subfolder / date_str
    
    def get_option_data_path(self, date_str: str, option_type: str) -> Path:
        """Get path to specific option type data"""
        valid_types = ["CE_BUY", "CE_SELL", "PE_BUY", "PE_SELL"]
        if option_type not in valid_types:
            raise ValueError(f"Invalid option_type: {option_type}. Must be one of {valid_types}")
        
        return self.get_data_path(date_str) / option_type
    
    def get_threshold(self, vix: float, fixed_threshold: Optional[float] = None) -> float:
        """Calculate threshold based on VIX (replicated from Fyers_VIX_Threshold.py)"""
        if fixed_threshold is not None:
            return round(fixed_threshold, 2)
        
        if vix <= 9:
            threshold = 0.28
        elif 9 < vix < 23:
            threshold = 0.3 + 0.02 * (vix - 10)
        else:
            threshold = 0.45
            
        threshold = threshold - self.vix_threshold_diff
        return round(threshold, 2)
    
    def get_sell_option_price(self, vix: float) -> int:
        """Get sell option price based on VIX"""
        vix_int = int(round(vix))
        return self.vix_price_map.get(vix_int, 23)  # Default to 23 if VIX not in map
    
    def to_dict(self) -> Dict:
        """Convert configuration to dictionary"""
        return {
            'data_root_path': self.data_root_path,
            'data_subfolder': self.data_subfolder,
            'global_qty': self.global_qty,
            'start_time': self.start_time.strftime('%H:%M:%S'),
            'exit_time': self.exit_time.strftime('%H:%M:%S'),
            'delta_check_interval': self.delta_check_interval,
            'vix_threshold_diff': self.vix_threshold_diff,
            'expiry_day': self.expiry_day,
            'start_date': self.start_date,
            'end_date': self.end_date,
            'initial_capital': self.initial_capital,
            'commission_per_trade': self.commission_per_trade,
            'slippage_bps': self.slippage_bps,
            'max_loss_per_day': self.max_loss_per_day,
            'max_profit_per_day': self.max_profit_per_day,
            'results_dir': self.results_dir,
            'save_trades': self.save_trades,
            'save_daily_pnl': self.save_daily_pnl,
            'generate_plots': self.generate_plots
        }


# Default configuration instance
DEFAULT_CONFIG = BacktestConfig()
