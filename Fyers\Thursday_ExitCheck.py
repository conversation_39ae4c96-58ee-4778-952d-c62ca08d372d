from Fyers_Utility import initialize_fyers
from Fyers_VIX_Threshold import *
#from Fyers_Get_Nifty_Atm_Strike import get_nifty_atm_strike
from datetime import datetime
from Fyers_MarketCloseTimeCheck import *
from Fyers_Utility import *
from logger_config import setup_logger
import time, sys
from Fyers.Fyers_save_final_pnl import save_final_pnl

# Setup logger
#logger = setup_logger('main_logger', 'main.log')
logger = get_logger('main_logger')

def thursday_ExitCheck(fyers):

    while True:
        try:
            now = datetime.now()
            # Exit the script at 3:31:00 PM
            if now.hour == 15 and now.minute >= 32:
                    logger.info("It's past 15:32. Initiating end-of-day procedures.")
                    save_final_pnl(fyers)                              
                    logger.info("End-of-day procedures completed. Exiting the script.")
                    sys.exit()

            time.sleep(1)  # Sleep for a second to avoid unnecessary loop iterations    
        
        except ValueError as e:  # Example of a specific exception
            logger.error(f"A value error occurred: {e}")
        except Exception as e:
            logger.error(f"An error occurred: {e}")
            time.sleep(1)
#---------------------------------------------------------------------------------------------------------
fyers = initialize_fyers()
thursday_ExitCheck(fyers)
