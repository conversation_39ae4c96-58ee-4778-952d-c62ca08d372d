
# Function to fetch total P&L for both open and closed positions
def get_total_pnl(fyers):
    
    # Fetch open positions
    positions_data = fyers.positions()
    
    if positions_data.get("code") == 200 and "netPositions" in positions_data:
        net_positions = positions_data.get("netPositions", [])
        
        total_pnl = 0.0
        open_pnl = 0.0
        closed_pnl = 0.0
        
        for position in net_positions:
            if "NIFTY" in position.get("symbol") and "BANKNIFTY" not in position.get("symbol") and position.get("productType")=="MARGIN":
                
                total_pnl += position.get("pl", 0.0)
                open_pnl += position.get("unrealized_profit", 0.0)
                closed_pnl += position.get("realized_profit", 0.0)
        
        total_pnl = round(total_pnl, 2)
        open_pnl = round(open_pnl, 2)
        closed_pnl = round(closed_pnl, 2)
        
    return total_pnl, open_pnl, closed_pnl


#-----------------------------------------------------------------------------------------
#fyers = initialize_fyers()
#get_total_pnl(fyers)