def get_order_status(interactive_symphony, clientID=None, app_order_id=None):
    """Request Order book gives states of all the orders placed by an user"""
    try:
        params = {}
        if not interactive_symphony.isInvestorClient:
            params['clientID'] = clientID
        response = interactive_symphony._get("order.status", params)
        #print(response)  # For debugging, remove in production
        
        # Check for the specific order using the AppOrderID
        if 'result' in response:
            for order in response['result']:
                if order.get('AppOrderID') == app_order_id:
                    # Check if the order is filled
                    if order.get('OrderStatus') == 'Filled':
                        return True, None  # Order was filled successfully, no reason needed
                    else:
                        # If the order is not filled, check for the CancelRejectReason
                        cancel_reject_reason = order.get('CancelRejectReason')
                        return False, cancel_reject_reason  # Return reason if not filled
        
        # If the AppOrderID was not found or no result
        return False, "Order not found in order book"

    except Exception as e:
        print(f"Error: {e}")
        return False, str(e)  # Return error message in case of an exception