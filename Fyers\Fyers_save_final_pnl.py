from datetime import datetime
import os, re
import pandas as pd
from dotenv import load_dotenv
import logger_config
from Get_Total_PNL import get_total_pnl
from reportlab.lib.pagesizes import A4
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.lib.units import cm

# Logger
logger = logger_config.get_logger('main_logger')

# Load environment variables
load_dotenv()

#------------------------------------------------------------------------------------------------
# Ensure Monthly_PNL_Data folder exists
def ensure_pnl_data_directory():
    folder_path = "Monthly_PNL_Data"
    if not os.path.exists(folder_path):
        os.makedirs(folder_path)
        logger.info(f"Created folder: {folder_path}")
    return folder_path

#------------------------------------------------------------------------------------------------
# Get file name for the current month
def get_file_name(current_datetime):
    folder_path = ensure_pnl_data_directory()
    file_name = f"{current_datetime.strftime('%B_%Y')}.csv"
    file_path = os.path.join(folder_path, file_name)

    header = ["Date", "Start_Time", "Exit_Time","Capital","Day_High", 
              "Day_Low", "Daily_P&L", "Daily_P&L %","Monthly_P&L", "Monthly_P&L %"]

    if not os.path.exists(file_path):
        pd.DataFrame(columns=header).to_csv(file_path, index=False)
        logger.info(f"Created monthly file: {file_path}")
    else:
        logger.info(f"Using existing monthly file: {file_path}")

    return file_path

#------------------------------------------------------------------------------------------------
# Save daily data to CSV
def save_data_to_csv(file_name, pnl_df):
    pnl_df.to_csv(file_name, mode='a', index=False, header=not os.path.exists(file_name))
    logger.info(f"Appended new PnL data to {file_name}\n{pnl_df}")

#------------------------------------------------------------------------------------------------
# Compute cumulative monthly PnL
def get_cumulative_pnl(file_name, total_pnl):
    try:
        existing_data = pd.read_csv(file_name)
        if not existing_data.empty:
            cumulative_pnl = existing_data["Monthly_P&L"].iloc[-1] + total_pnl
        else:
            cumulative_pnl = total_pnl
    except FileNotFoundError:
        cumulative_pnl = total_pnl
    return cumulative_pnl

#------------------------------------------------------------------------------------------------
# Extract day's high and low PnL from logs
def get_day_high_low_pnl():
    current_datetime = datetime.now()  
    current_date = current_datetime.strftime("%d-%m-%Y")
    month_folder = current_datetime.strftime("%B_%Y")
    log_file_path = f"logs/{month_folder}/{current_date}.log"

    day_high_pnl = float('-inf')
    day_low_pnl = float('inf')

    try:
        with open(log_file_path, 'r') as file:
            log_content = file.read()
            pnl_matches = re.findall(r'Total PNL: ([-]?\d+\.?\d*)', log_content)
            if pnl_matches:
                pnl_values = [float(pnl) for pnl in pnl_matches]
                day_high_pnl = max(pnl_values)
                day_low_pnl = min(pnl_values)
                logger.info(f"Extracted Day High PNL: {day_high_pnl}, Day Low PNL: {day_low_pnl}")
            else:
                logger.info("No PNL values found in today's log file")
    except FileNotFoundError:
        logger.info(f"Log file not found: {log_file_path}")
    except Exception as e:
        logger.info(f"Error reading log file: {str(e)}")

    return day_high_pnl, day_low_pnl

#------------------------------------------------------------------------------------------------
# Save DataFrame as PDF
def save_data_to_pdf(pdf_file_name, pnl_df):
    pdf = SimpleDocTemplate(pdf_file_name, pagesize=A4,
                            rightMargin=20, leftMargin=20,
                            topMargin=20, bottomMargin=20)
    
    # Styles for wrapping text
    styles = getSampleStyleSheet()
    styleN = styles['Normal']

    # Convert all cells to Paragraphs to enable wrapping
    data = []
    header = [Paragraph(str(col), styles['Heading6']) for col in pnl_df.columns.tolist()]
    data.append(header)
    
    for row in pnl_df.values.tolist():
        new_row = []
        for item in row:
            new_row.append(Paragraph(str(item), styleN))
        data.append(new_row)
    
    # Calculate column widths (adjust as needed)
    page_width = A4[0] - pdf.leftMargin - pdf.rightMargin
    num_cols = len(data[0])
    col_widths = [page_width / num_cols] * num_cols  # evenly distributed
    
    table = Table(data, colWidths=col_widths, repeatRows=1)
    
    # Table style
    style = TableStyle([
        ('BACKGROUND', (0,0), (-1,0), colors.grey),
        ('TEXTCOLOR', (0,0), (-1,0), colors.whitesmoke),
        ('ALIGN', (0,0), (-1,-1), 'CENTER'),
        ('FONTNAME', (0,0), (-1,0), 'Helvetica-Bold'),
        ('FONTSIZE', (0,0), (-1,0), 10),
        ('BOTTOMPADDING', (0,0), (-1,0), 8),
        ('GRID', (0,0), (-1,-1), 1, colors.black)
    ])
    
    # Columns to color
    pnl_columns = ["Daily_P&L", "Monthly_P&L"]

    # Find their indexes in the DataFrame
    pnl_col_indexes = [list(pnl_df.columns).index(col) for col in pnl_columns]

    # Apply coloring for each column
    for i, row in enumerate(pnl_df.values.tolist(), start=1):  # start=1 skips header
        for col_index in pnl_col_indexes:
            pnl_value = row[col_index]
            if pnl_value >= 0:
                style.add('TEXTCOLOR', (col_index, i), (col_index, i), colors.green)
            else:
                style.add('TEXTCOLOR', (col_index, i), (col_index, i), colors.red)

    table.setStyle(style)
    
    pdf.build([table])
    print(f"PDF updated: {pdf_file_name}")

#------------------------------------------------------------------------------------------------
# Generate PDF from full CSV
def save_pdf_from_csv(csv_file_path):
    if os.path.exists(csv_file_path):
        pnl_df = pd.read_csv(csv_file_path)
        pdf_file_name = csv_file_path.replace(".csv", ".pdf")
        save_data_to_pdf(pdf_file_name, pnl_df)

#------------------------------------------------------------------------------------------------
# Save final PnL (CSV + PDF)
def save_final_pnl(broker, start_time, exit_time):
    capital = float(os.getenv("Capital"))
    current_datetime = datetime.now()
    current_date = current_datetime.strftime("%d-%m-%Y")

    # CSV file
    file_name = get_file_name(current_datetime)

    # Get PnL
    total_pnl, open_pnl, closed_pnl = get_total_pnl(broker)
    cumulative_pnl = get_cumulative_pnl(file_name, total_pnl)

    pnl_percentage = round((total_pnl / capital) * 100, 2) if capital != 0 else 0
    monthly_pnl_percentage = round((cumulative_pnl / capital) * 100, 2) if capital != 0 else 0

    day_high_pnl, day_low_pnl = get_day_high_low_pnl()

    pnl_df = pd.DataFrame([[current_date, start_time, exit_time, capital, day_high_pnl, day_low_pnl, total_pnl, pnl_percentage,cumulative_pnl, 
                            monthly_pnl_percentage]], 
                          columns=["Date", "Start_Time", "Exit_Time","Capital","Day_High", "Day_Low", "Daily_P&L", "Daily_P&L %","Monthly_P&L", 
                                     "Monthly_P&L %"])

    # Save CSV
    save_data_to_csv(file_name, pnl_df)

    # Update PDF from full CSV
    save_pdf_from_csv(file_name)

