import os
import time
import binascii


class TraceId:
    """
    A trace ID tracks the path of a request through your application.
    A trace collects all the segments generated by a single request.
    A trace ID is required for a segment.
    """
    VERSION = '1'
    DELIMITER = '-'

    def __init__(self):
        """
        Generate a random trace id.
        """
        self.start_time = int(time.time())
        self.__number = binascii.b2a_hex(os.urandom(12)).decode('utf-8')

    def to_id(self):
        """
        Convert TraceId object to a string.
        """
        return "%s%s%s%s%s" % (TraceId.VERSION, TraceId.DELIMITER,
                               format(self.start_time, 'x'),
                               TraceId.DELIMITER, self.__number)
