"""
Data Loader Module for Backtesting Framework
Handles loading and processing of historical parquet data
"""

import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union
from datetime import datetime, time
import logging
from .config import BacktestConfig

logger = logging.getLogger(__name__)


class DataLoader:
    """Loads and processes historical option data from parquet files"""
    
    def __init__(self, config: BacktestConfig):
        self.config = config
        self.data_cache = {}
        
    def get_available_dates(self) -> List[str]:
        """Get list of available trading dates from data directory"""
        data_path = Path(self.config.data_root_path) / self.config.data_subfolder
        
        if not data_path.exists():
            logger.error(f"Data path does not exist: {data_path}")
            return []
        
        # Get all date directories (format: YYYYMMDD)
        date_dirs = [d.name for d in data_path.iterdir() 
                    if d.is_dir() and d.name.isdigit() and len(d.name) == 8]
        
        # Filter by date range if specified
        if self.config.start_date or self.config.end_date:
            filtered_dates = []
            for date_str in date_dirs:
                if self.config.start_date and date_str < self.config.start_date:
                    continue
                if self.config.end_date and date_str > self.config.end_date:
                    continue
                filtered_dates.append(date_str)
            date_dirs = filtered_dates
        
        return sorted(date_dirs)
    
    def load_option_data(self, date_str: str, option_type: str) -> Optional[pd.DataFrame]:
        """Load option data for a specific date and option type"""
        cache_key = f"{date_str}_{option_type}"
        
        if cache_key in self.data_cache:
            return self.data_cache[cache_key]
        
        try:
            option_path = self.config.get_option_data_path(date_str, option_type)
            
            if not option_path.exists():
                logger.warning(f"Option data path does not exist: {option_path}")
                return None
            
            # Find parquet files in the directory
            parquet_files = list(option_path.glob("*.parquet"))
            
            if not parquet_files:
                logger.warning(f"No parquet files found in: {option_path}")
                return None
            
            # Load and combine all parquet files
            dataframes = []
            for file_path in parquet_files:
                try:
                    df = pd.read_parquet(file_path)
                    dataframes.append(df)
                except Exception as e:
                    logger.error(f"Error loading {file_path}: {e}")
                    continue
            
            if not dataframes:
                logger.warning(f"No valid parquet files loaded from: {option_path}")
                return None
            
            # Combine all dataframes
            combined_df = pd.concat(dataframes, ignore_index=True)
            
            # Standardize column names and data types
            combined_df = self._standardize_dataframe(combined_df)
            
            # Cache the data
            self.data_cache[cache_key] = combined_df
            
            logger.info(f"Loaded {len(combined_df)} records for {date_str} {option_type}")
            return combined_df
            
        except Exception as e:
            logger.error(f"Error loading option data for {date_str} {option_type}: {e}")
            return None
    
    def _standardize_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """Standardize dataframe columns and data types"""
        # Expected columns (adjust based on your actual data structure)
        expected_columns = ['timestamp', 'symbol', 'ltp', 'bid', 'ask', 'volume', 'oi']
        
        # Convert timestamp to datetime if it's not already
        if 'timestamp' in df.columns:
            df['timestamp'] = pd.to_datetime(df['timestamp'])
        elif 'time' in df.columns:
            df['timestamp'] = pd.to_datetime(df['time'])
            df = df.drop('time', axis=1)
        
        # Ensure numeric columns are properly typed
        numeric_columns = ['ltp', 'bid', 'ask', 'volume', 'oi']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        # Sort by timestamp
        if 'timestamp' in df.columns:
            df = df.sort_values('timestamp').reset_index(drop=True)
        
        return df
    
    def load_day_data(self, date_str: str) -> Dict[str, pd.DataFrame]:
        """Load all option data for a specific trading day"""
        option_types = ["CE_BUY", "CE_SELL", "PE_BUY", "PE_SELL"]
        day_data = {}
        
        for option_type in option_types:
            data = self.load_option_data(date_str, option_type)
            if data is not None:
                day_data[option_type] = data
        
        return day_data
    
    def get_symbols_for_date(self, date_str: str) -> Dict[str, List[str]]:
        """Get available symbols for each option type on a specific date"""
        symbols = {}
        option_types = ["CE_BUY", "CE_SELL", "PE_BUY", "PE_SELL"]
        
        for option_type in option_types:
            data = self.load_option_data(date_str, option_type)
            if data is not None and 'symbol' in data.columns:
                symbols[option_type] = data['symbol'].unique().tolist()
            else:
                symbols[option_type] = []
        
        return symbols
    
    def get_price_data(self, date_str: str, symbol: str, option_type: str, 
                      start_time: Optional[time] = None, 
                      end_time: Optional[time] = None) -> Optional[pd.DataFrame]:
        """Get price data for a specific symbol and time range"""
        data = self.load_option_data(date_str, option_type)
        
        if data is None or 'symbol' not in data.columns:
            return None
        
        # Filter by symbol
        symbol_data = data[data['symbol'] == symbol].copy()
        
        if symbol_data.empty:
            return None
        
        # Filter by time range if specified
        if start_time or end_time:
            if 'timestamp' in symbol_data.columns:
                symbol_data['time'] = symbol_data['timestamp'].dt.time
                
                if start_time:
                    symbol_data = symbol_data[symbol_data['time'] >= start_time]
                
                if end_time:
                    symbol_data = symbol_data[symbol_data['time'] <= end_time]
                
                symbol_data = symbol_data.drop('time', axis=1)
        
        return symbol_data.reset_index(drop=True)
    
    def get_vix_data(self, date_str: str) -> Optional[float]:
        """Get VIX value for a specific date (mock implementation)"""
        # In a real implementation, you would load VIX data from your data source
        # For now, return a mock VIX value based on date
        try:
            date_obj = datetime.strptime(date_str, "%Y%m%d")
            # Mock VIX calculation - replace with actual VIX data loading
            mock_vix = 12.0 + (hash(date_str) % 10)  # VIX between 12-22
            return float(mock_vix)
        except:
            return 15.0  # Default VIX value
    
    def clear_cache(self):
        """Clear the data cache to free memory"""
        self.data_cache.clear()
        logger.info("Data cache cleared")
    
    def get_cache_info(self) -> Dict:
        """Get information about cached data"""
        return {
            'cached_datasets': len(self.data_cache),
            'cache_keys': list(self.data_cache.keys())
        }
