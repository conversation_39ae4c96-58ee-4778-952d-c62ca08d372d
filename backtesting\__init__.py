# Backtesting Framework for BCS Strategy
# This package contains modules for backtesting the Balanced Calendar Spread strategy

__version__ = "1.0.0"
__author__ = "BCS Strategy Backtesting Framework"

# Import main modules
from .config import BacktestConfig
from .data_loader import DataLoader
from .market_simulator import MarketDataSimulator
from .strategy_backtester import StrategyBacktester
from .portfolio_manager import PortfolioManager
from .results_analyzer import ResultsAnalyzer
from .main_runner import BacktestRunner

__all__ = [
    'BacktestConfig',
    'DataLoader', 
    'MarketDataSimulator',
    'StrategyBacktester',
    'PortfolioManager',
    'ResultsAnalyzer',
    'BacktestRunner'
]
