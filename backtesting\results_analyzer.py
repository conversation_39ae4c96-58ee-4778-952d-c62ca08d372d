"""
Results Analysis Module
Provides comprehensive analysis and reporting of backtesting results
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
from pathlib import Path
import json
from .config import BacktestConfig
from .portfolio_manager import PortfolioManager, Trade

logger = logging.getLogger(__name__)


class ResultsAnalyzer:
    """Analyzes and reports backtesting results"""
    
    def __init__(self, config: BacktestConfig):
        self.config = config
        self.results_dir = Path(config.results_dir)
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
    def analyze_backtest_results(self, portfolio_history: List[Dict], 
                               trades: List[Trade], 
                               daily_summaries: List[Dict]) -> Dict:
        """Perform comprehensive analysis of backtest results"""
        try:
            # Convert data to DataFrames for analysis
            portfolio_df = pd.DataFrame(portfolio_history)
            trades_df = pd.DataFrame([trade.to_dict() for trade in trades])
            daily_df = pd.DataFrame(daily_summaries)
            
            # Calculate performance metrics
            performance_metrics = self._calculate_performance_metrics(portfolio_df, trades_df, daily_df)
            
            # Analyze trades
            trade_analysis = self._analyze_trades(trades_df)
            
            # Risk analysis
            risk_analysis = self._calculate_risk_metrics(portfolio_df, daily_df)
            
            # Strategy-specific analysis
            strategy_analysis = self._analyze_strategy_performance(trades_df, daily_df)
            
            # Combine all results
            results = {
                'performance_metrics': performance_metrics,
                'trade_analysis': trade_analysis,
                'risk_analysis': risk_analysis,
                'strategy_analysis': strategy_analysis,
                'summary_stats': self._generate_summary_stats(portfolio_df, trades_df, daily_df)
            }
            
            # Save results
            self._save_results(results, portfolio_df, trades_df, daily_df)
            
            # Generate plots if enabled
            if self.config.generate_plots:
                self._generate_plots(portfolio_df, trades_df, daily_df)
            
            return results
            
        except Exception as e:
            logger.error(f"Error analyzing backtest results: {e}")
            return {}
    
    def _calculate_performance_metrics(self, portfolio_df: pd.DataFrame, 
                                     trades_df: pd.DataFrame, 
                                     daily_df: pd.DataFrame) -> Dict:
        """Calculate key performance metrics"""
        try:
            if portfolio_df.empty:
                return {}
            
            # Basic metrics
            initial_capital = portfolio_df['initial_capital'].iloc[0]
            final_capital = portfolio_df['current_capital'].iloc[-1]
            total_return = (final_capital - initial_capital) / initial_capital * 100
            
            # Daily returns
            daily_returns = daily_df['daily_pnl_pct'].values if 'daily_pnl_pct' in daily_df.columns else []
            
            # Calculate additional metrics
            metrics = {
                'initial_capital': initial_capital,
                'final_capital': final_capital,
                'total_return_pct': total_return,
                'total_pnl': final_capital - initial_capital,
                'max_drawdown_pct': portfolio_df['max_drawdown'].max() * 100 if 'max_drawdown' in portfolio_df.columns else 0,
                'total_trades': len(trades_df),
                'winning_trades': len(trades_df[trades_df['realized_pnl'] > 0]) if not trades_df.empty else 0,
                'losing_trades': len(trades_df[trades_df['realized_pnl'] < 0]) if not trades_df.empty else 0,
                'win_rate_pct': 0,
                'avg_win': 0,
                'avg_loss': 0,
                'profit_factor': 0,
                'sharpe_ratio': 0,
                'sortino_ratio': 0,
                'calmar_ratio': 0
            }
            
            # Calculate win rate
            if metrics['total_trades'] > 0:
                metrics['win_rate_pct'] = (metrics['winning_trades'] / metrics['total_trades']) * 100
            
            # Calculate average win/loss
            if not trades_df.empty:
                winning_trades = trades_df[trades_df['realized_pnl'] > 0]['realized_pnl']
                losing_trades = trades_df[trades_df['realized_pnl'] < 0]['realized_pnl']
                
                if len(winning_trades) > 0:
                    metrics['avg_win'] = winning_trades.mean()
                if len(losing_trades) > 0:
                    metrics['avg_loss'] = abs(losing_trades.mean())
                
                # Profit factor
                total_wins = winning_trades.sum() if len(winning_trades) > 0 else 0
                total_losses = abs(losing_trades.sum()) if len(losing_trades) > 0 else 0
                if total_losses > 0:
                    metrics['profit_factor'] = total_wins / total_losses
            
            # Calculate ratios if we have daily returns
            if len(daily_returns) > 1:
                returns_std = np.std(daily_returns)
                if returns_std > 0:
                    metrics['sharpe_ratio'] = np.mean(daily_returns) / returns_std * np.sqrt(252)
                    
                    # Sortino ratio (downside deviation)
                    downside_returns = daily_returns[daily_returns < 0]
                    if len(downside_returns) > 0:
                        downside_std = np.std(downside_returns)
                        if downside_std > 0:
                            metrics['sortino_ratio'] = np.mean(daily_returns) / downside_std * np.sqrt(252)
                
                # Calmar ratio
                max_dd = metrics['max_drawdown_pct'] / 100
                if max_dd > 0:
                    annualized_return = total_return / 100  # Assuming this is for the full period
                    metrics['calmar_ratio'] = annualized_return / max_dd
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return {}
    
    def _analyze_trades(self, trades_df: pd.DataFrame) -> Dict:
        """Analyze individual trades"""
        try:
            if trades_df.empty:
                return {'total_trades': 0}
            
            analysis = {
                'total_trades': len(trades_df),
                'trades_by_type': trades_df['trade_type'].value_counts().to_dict(),
                'trades_by_side': trades_df['side'].value_counts().to_dict(),
                'avg_trade_duration_minutes': trades_df['duration_minutes'].mean(),
                'avg_trade_pnl': trades_df['realized_pnl'].mean(),
                'best_trade': trades_df['realized_pnl'].max(),
                'worst_trade': trades_df['realized_pnl'].min(),
                'total_commission': trades_df['commission'].sum(),
                'trades_per_day': {},
                'hourly_trade_distribution': {}
            }
            
            # Trades per day
            if 'entry_time' in trades_df.columns:
                trades_df['entry_date'] = pd.to_datetime(trades_df['entry_time']).dt.date
                analysis['trades_per_day'] = trades_df['entry_date'].value_counts().to_dict()
                
                # Hourly distribution
                trades_df['entry_hour'] = pd.to_datetime(trades_df['entry_time']).dt.hour
                analysis['hourly_trade_distribution'] = trades_df['entry_hour'].value_counts().to_dict()
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing trades: {e}")
            return {}
    
    def _calculate_risk_metrics(self, portfolio_df: pd.DataFrame, daily_df: pd.DataFrame) -> Dict:
        """Calculate risk-related metrics"""
        try:
            risk_metrics = {
                'max_drawdown_pct': 0,
                'max_drawdown_duration_days': 0,
                'var_95': 0,  # Value at Risk (95%)
                'cvar_95': 0,  # Conditional Value at Risk (95%)
                'volatility_annualized': 0,
                'downside_volatility': 0,
                'max_daily_loss': 0,
                'max_daily_gain': 0,
                'consecutive_losing_days': 0,
                'consecutive_winning_days': 0
            }
            
            if not daily_df.empty and 'daily_pnl' in daily_df.columns:
                daily_pnl = daily_df['daily_pnl'].values
                
                # Basic risk metrics
                risk_metrics['max_daily_loss'] = daily_pnl.min()
                risk_metrics['max_daily_gain'] = daily_pnl.max()
                
                # VaR and CVaR
                if len(daily_pnl) > 0:
                    risk_metrics['var_95'] = np.percentile(daily_pnl, 5)
                    risk_metrics['cvar_95'] = daily_pnl[daily_pnl <= risk_metrics['var_95']].mean()
                
                # Volatility
                if len(daily_pnl) > 1:
                    daily_returns = daily_df['daily_pnl_pct'].values if 'daily_pnl_pct' in daily_df.columns else []
                    if len(daily_returns) > 1:
                        risk_metrics['volatility_annualized'] = np.std(daily_returns) * np.sqrt(252)
                        
                        # Downside volatility
                        downside_returns = daily_returns[daily_returns < 0]
                        if len(downside_returns) > 0:
                            risk_metrics['downside_volatility'] = np.std(downside_returns) * np.sqrt(252)
                
                # Consecutive days analysis
                winning_days = daily_pnl > 0
                losing_days = daily_pnl < 0
                
                risk_metrics['consecutive_winning_days'] = self._max_consecutive(winning_days)
                risk_metrics['consecutive_losing_days'] = self._max_consecutive(losing_days)
            
            # Drawdown analysis from portfolio data
            if not portfolio_df.empty and 'max_drawdown' in portfolio_df.columns:
                risk_metrics['max_drawdown_pct'] = portfolio_df['max_drawdown'].max() * 100
            
            return risk_metrics
            
        except Exception as e:
            logger.error(f"Error calculating risk metrics: {e}")
            return {}
    
    def _max_consecutive(self, boolean_series: np.ndarray) -> int:
        """Calculate maximum consecutive True values"""
        if len(boolean_series) == 0:
            return 0
        
        max_consecutive = 0
        current_consecutive = 0
        
        for value in boolean_series:
            if value:
                current_consecutive += 1
                max_consecutive = max(max_consecutive, current_consecutive)
            else:
                current_consecutive = 0
        
        return max_consecutive
    
    def _analyze_strategy_performance(self, trades_df: pd.DataFrame, daily_df: pd.DataFrame) -> Dict:
        """Analyze BCS strategy-specific performance"""
        try:
            strategy_analysis = {
                'delta_adjustments': 0,
                'strategy_recreations': 0,
                'square_offs': 0,
                'avg_holding_period_minutes': 0,
                'success_rate_by_action': {},
                'pnl_by_action': {}
            }
            
            if not trades_df.empty:
                # Count different trade types
                trade_types = trades_df['trade_type'].value_counts()
                strategy_analysis['delta_adjustments'] = trade_types.get('delta_adjustment', 0)
                strategy_analysis['strategy_recreations'] = trade_types.get('position_update', 0)
                strategy_analysis['square_offs'] = trade_types.get('square_off', 0)
                
                # Average holding period
                if 'duration_minutes' in trades_df.columns:
                    strategy_analysis['avg_holding_period_minutes'] = trades_df['duration_minutes'].mean()
                
                # PnL by action type
                pnl_by_type = trades_df.groupby('trade_type')['realized_pnl'].agg(['sum', 'mean', 'count'])
                strategy_analysis['pnl_by_action'] = pnl_by_type.to_dict()
            
            return strategy_analysis
            
        except Exception as e:
            logger.error(f"Error analyzing strategy performance: {e}")
            return {}
    
    def _generate_summary_stats(self, portfolio_df: pd.DataFrame, 
                              trades_df: pd.DataFrame, 
                              daily_df: pd.DataFrame) -> Dict:
        """Generate summary statistics"""
        return {
            'backtest_period': {
                'start_date': self.config.start_date,
                'end_date': self.config.end_date,
                'total_days': len(daily_df)
            },
            'data_quality': {
                'portfolio_records': len(portfolio_df),
                'trade_records': len(trades_df),
                'daily_records': len(daily_df)
            },
            'configuration': self.config.to_dict()
        }
    
    def _save_results(self, results: Dict, portfolio_df: pd.DataFrame, 
                     trades_df: pd.DataFrame, daily_df: pd.DataFrame):
        """Save results to files"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Save summary results as JSON
            results_file = self.results_dir / f"backtest_results_{timestamp}.json"
            with open(results_file, 'w') as f:
                # Convert numpy types to native Python types for JSON serialization
                json_results = self._convert_numpy_types(results)
                json.dump(json_results, f, indent=2, default=str)
            
            # Save detailed data as CSV
            if self.config.save_trades and not trades_df.empty:
                trades_file = self.results_dir / f"trades_{timestamp}.csv"
                trades_df.to_csv(trades_file, index=False)
            
            if self.config.save_daily_pnl and not daily_df.empty:
                daily_file = self.results_dir / f"daily_pnl_{timestamp}.csv"
                daily_df.to_csv(daily_file, index=False)
            
            # Save portfolio history
            portfolio_file = self.results_dir / f"portfolio_history_{timestamp}.csv"
            portfolio_df.to_csv(portfolio_file, index=False)
            
            logger.info(f"Results saved to {self.results_dir}")
            
        except Exception as e:
            logger.error(f"Error saving results: {e}")
    
    def _convert_numpy_types(self, obj):
        """Convert numpy types to native Python types for JSON serialization"""
        if isinstance(obj, dict):
            return {key: self._convert_numpy_types(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._convert_numpy_types(item) for item in obj]
        elif isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        else:
            return obj
    
    def _generate_plots(self, portfolio_df: pd.DataFrame, trades_df: pd.DataFrame, daily_df: pd.DataFrame):
        """Generate visualization plots"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Set up the plotting style
            plt.style.use('seaborn-v0_8')
            
            # 1. Portfolio value over time
            if not portfolio_df.empty and 'current_capital' in portfolio_df.columns:
                plt.figure(figsize=(12, 6))
                plt.plot(portfolio_df.index, portfolio_df['current_capital'])
                plt.title('Portfolio Value Over Time')
                plt.xlabel('Time')
                plt.ylabel('Portfolio Value')
                plt.grid(True)
                plt.tight_layout()
                plt.savefig(self.results_dir / f"portfolio_value_{timestamp}.png")
                plt.close()
            
            # 2. Daily PnL distribution
            if not daily_df.empty and 'daily_pnl' in daily_df.columns:
                plt.figure(figsize=(10, 6))
                plt.hist(daily_df['daily_pnl'], bins=30, alpha=0.7, edgecolor='black')
                plt.title('Daily PnL Distribution')
                plt.xlabel('Daily PnL')
                plt.ylabel('Frequency')
                plt.grid(True, alpha=0.3)
                plt.tight_layout()
                plt.savefig(self.results_dir / f"daily_pnl_distribution_{timestamp}.png")
                plt.close()
            
            # 3. Trade PnL scatter plot
            if not trades_df.empty and 'realized_pnl' in trades_df.columns:
                plt.figure(figsize=(12, 6))
                colors = ['green' if pnl > 0 else 'red' for pnl in trades_df['realized_pnl']]
                plt.scatter(range(len(trades_df)), trades_df['realized_pnl'], c=colors, alpha=0.6)
                plt.title('Trade PnL Over Time')
                plt.xlabel('Trade Number')
                plt.ylabel('Realized PnL')
                plt.axhline(y=0, color='black', linestyle='--', alpha=0.5)
                plt.grid(True, alpha=0.3)
                plt.tight_layout()
                plt.savefig(self.results_dir / f"trade_pnl_{timestamp}.png")
                plt.close()
            
            logger.info(f"Plots saved to {self.results_dir}")
            
        except Exception as e:
            logger.error(f"Error generating plots: {e}")
    
    def print_summary_report(self, results: Dict):
        """Print a formatted summary report"""
        try:
            print("\n" + "="*80)
            print("BACKTESTING RESULTS SUMMARY")
            print("="*80)
            
            # Performance metrics
            perf = results.get('performance_metrics', {})
            print(f"\nPERFORMANCE METRICS:")
            print(f"Initial Capital: ${perf.get('initial_capital', 0):,.2f}")
            print(f"Final Capital: ${perf.get('final_capital', 0):,.2f}")
            print(f"Total Return: {perf.get('total_return_pct', 0):.2f}%")
            print(f"Total PnL: ${perf.get('total_pnl', 0):,.2f}")
            print(f"Max Drawdown: {perf.get('max_drawdown_pct', 0):.2f}%")
            print(f"Sharpe Ratio: {perf.get('sharpe_ratio', 0):.2f}")
            print(f"Win Rate: {perf.get('win_rate_pct', 0):.2f}%")
            
            # Trade analysis
            trade_analysis = results.get('trade_analysis', {})
            print(f"\nTRADE ANALYSIS:")
            print(f"Total Trades: {trade_analysis.get('total_trades', 0)}")
            print(f"Average Trade PnL: ${trade_analysis.get('avg_trade_pnl', 0):.2f}")
            print(f"Best Trade: ${trade_analysis.get('best_trade', 0):.2f}")
            print(f"Worst Trade: ${trade_analysis.get('worst_trade', 0):.2f}")
            
            # Strategy analysis
            strategy = results.get('strategy_analysis', {})
            print(f"\nSTRATEGY ANALYSIS:")
            print(f"Delta Adjustments: {strategy.get('delta_adjustments', 0)}")
            print(f"Strategy Recreations: {strategy.get('strategy_recreations', 0)}")
            print(f"Square Offs: {strategy.get('square_offs', 0)}")
            
            print("\n" + "="*80)
            
        except Exception as e:
            logger.error(f"Error printing summary report: {e}")
