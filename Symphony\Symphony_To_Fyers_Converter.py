
from config import BrokerContext
from Symphony import Symphony_Get_Masterdf

def convert_to_fyers_symbol(exchange_instrument_id):
    masterdf = Symphony_Get_Masterdf.get_masterdf(BrokerContext().market_symphony)
    df = masterdf[(masterdf['ExchangeInstrumentID'] == exchange_instrument_id)]
    fyers_symbol = df["Description"].apply(lambda x: f"NSE:{x}")
    
    # Return only the first symbol as a string
    return fyers_symbol.iloc[0] if not fyers_symbol.empty else None

