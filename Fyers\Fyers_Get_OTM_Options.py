import json
from datetime import datetime
from fyers_apiv3 import fyersModel
import time
import pandas as pd
import logger_config 

#Get logger
#logger = setup_logger('main_logger', 'main.log')
logger = logger_config.get_logger('main_logger')

def get_nifty_otm_options(fyers, expiry_type, otm_count, symbol=None):
    logger.info("\nGetting Nifty OTM Options...")
    logger.info(f"Expiry Type:{expiry_type}")
    logger.info(f"OTM Count:{otm_count}")

    data = {
			"symbol": "NSE:NIFTY50-INDEX",
			"strikecount": 20,
			"timestamp": ""
		}
		
    response = fyers.optionchain(data)
    #print("Response:", response)
    
    expiries = response['data']['expiryData']
    #print("Expiries:", expiries)

    sorted_expiries = sorted(expiries, key=lambda x: x['expiry'])
    #print("Sorted Expiries:", sorted_expiries)

    # Adjust the logic to select the correct expiry
    if expiry_type == "current":
        selected_expiry = sorted_expiries[0]
    elif expiry_type == "next":
        selected_expiry = sorted_expiries[1]
    elif expiry_type == "next-next":
        selected_expiry = sorted_expiries[2]  # Adjusted to select the third expiry
    else:
        raise ValueError("Invalid expiry type specified.")

    data = {
			"symbol": "NSE:NIFTY50-INDEX",
			"strikecount": otm_count,
			"timestamp": selected_expiry['expiry']
		}
		
    response = fyers.optionchain(data)
    #print("Response:", response)

    option_chain = response['data']['optionsChain']
    #print("Option Chain:", option_chain)

    nifty_spot = option_chain[0]['ltp']
    #print("Nifty Spot:", nifty_spot)

    # Separate CE & PE options
    ce_options = sorted([item for item in option_chain if "CE" in item["symbol"]], key=lambda x: x["strike_price"])
    pe_options = sorted([item for item in option_chain if "PE" in item["symbol"]], key=lambda x: x["strike_price"])
    #print("CE Options:", ce_options)
    #print("PE Options:", pe_options)

    # Select specified number of OTM CE (strike price > spot price)
    otm_ce = [item for item in ce_options if item["strike_price"] > nifty_spot][:otm_count]
    otm_pe = [item for item in pe_options if item["strike_price"] < nifty_spot][-otm_count:]
    #print("OTM CE Options:", otm_ce)
    #print("OTM PE Options:", otm_pe)

    ce_dict = {item['symbol']: item['ltp'] for item in otm_ce}
    pe_dict = {item['symbol']: item['ltp'] for item in otm_pe}

    # Convert dictionaries to DataFrames
    ce_df = pd.DataFrame(list(ce_dict.items()), columns=['Symbol', 'LTP'])
    pe_df = pd.DataFrame(list(pe_dict.items()), columns=['Symbol', 'LTP'])

    # Print the DataFrames
    logger.info("CE DataFrame:")
    logger.info(ce_df)

    logger.info("\nPE DataFrame:")
    logger.info(pe_df)

    combined_dict = {"CE": ce_dict, "PE": pe_dict}
    #print("Combined Dict:", combined_dict)

    return combined_dict

#CLIENT_ID =  "O5MRPDE4TQ-100" 

#ACCESS_TOKEN = ""

#fyers = fyersModel.FyersModel(client_id=CLIENT_ID, token=ACCESS_TOKEN, is_async=False,log_path="")

#get_nifty_otm_options(fyers, expiry_type="current", otm_count=20)
#get_nifty_otm_options(fyers, expiry_type="next", otm_count=30)

