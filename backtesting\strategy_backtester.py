"""
Strategy Backtester Module
Implements the core BCS strategy logic for backtesting
"""

import pandas as pd
import numpy as np
from datetime import datetime, time
from typing import Dict, List, Optional, Tuple
import logging
import re
from .config import BacktestConfig
from .portfolio_manager import PortfolioManager
from .market_simulator import MarketDataSimulator

logger = logging.getLogger(__name__)


class StrategyBacktester:
    """Implements the Balanced Calendar Spread strategy for backtesting"""
    
    def __init__(self, config: BacktestConfig, portfolio_manager: PortfolioManager, 
                 market_simulator: MarketDataSimulator):
        self.config = config
        self.portfolio = portfolio_manager
        self.market = market_simulator
        
        # Strategy state
        self.strategy_initialized = False
        self.last_delta_check_time = None
        self.vix_value = 15.0
        
        # Mock option chain data (in real implementation, load from data)
        self.option_chain_cache = {}
        
    def initialize_strategy(self, date_str: str) -> bool:
        """Initialize the BCS strategy for a trading day"""
        try:
            # Get VIX value for the day
            self.vix_value = self.market.get_vix_value()
            logger.info(f"VIX value for {date_str}: {self.vix_value}")
            
            # Execute BCS strategy
            success = self.execute_bcs_strategy()
            
            if success:
                self.strategy_initialized = True
                logger.info(f"BCS strategy initialized successfully for {date_str}")
            else:
                logger.error(f"Failed to initialize BCS strategy for {date_str}")
            
            return success
            
        except Exception as e:
            logger.error(f"Error initializing strategy for {date_str}: {e}")
            return False
    
    def execute_bcs_strategy(self) -> bool:
        """Execute the Balanced Calendar Spread strategy"""
        try:
            # Get sell option price based on VIX
            sell_option_price = self.config.get_sell_option_price(self.vix_value)
            
            # Calculate calendar price (80% of sell option price)
            calendar_price = sell_option_price * 0.8
            
            # Get current weekday for resize calculation
            current_weekday = datetime.now().strftime("%A").upper()
            expiry_day = self.config.expiry_day
            
            # Apply resize factor
            resize = self.config.resize_values.get(expiry_day, {}).get(current_weekday, 1.0)
            sell_option_price *= resize
            calendar_price *= resize
            
            logger.info(f"Sell option price: {sell_option_price}, Calendar price: {calendar_price}")
            
            # Get ATM strike (mock implementation)
            atm_strike = self._get_mock_atm_strike()
            
            # Find option symbols based on prices
            option_symbols = self._find_option_symbols(atm_strike, sell_option_price, calendar_price)
            
            if not option_symbols:
                logger.error("Could not find suitable option symbols")
                return False
            
            # Place orders
            quantity = self.config.global_qty
            current_time = self.market.get_current_time()
            
            # Place BCS orders
            orders = [
                (option_symbols['sell_ce'], 'sell', quantity, 'CE_SHORT'),
                (option_symbols['sell_pe'], 'sell', quantity, 'PE_SHORT'),
                (option_symbols['buy_ce'], 'buy', quantity, 'CE_BUY'),
                (option_symbols['buy_pe'], 'buy', quantity, 'PE_BUY')
            ]
            
            for symbol, side, qty, pos_type in orders:
                execution = self.market.simulate_order_execution(symbol, side, qty, "limit")
                
                if execution['status'] == 'executed':
                    self.portfolio.add_position(
                        symbol=symbol,
                        side=side,
                        quantity=qty,
                        entry_price=execution['execution_price'],
                        entry_time=execution['execution_time'],
                        commission=execution['commission'],
                        position_type=pos_type
                    )
                else:
                    logger.error(f"Failed to execute order: {symbol} {side} {qty}")
                    return False
            
            logger.info("BCS strategy executed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error executing BCS strategy: {e}")
            return False
    
    def _get_mock_atm_strike(self) -> int:
        """Get mock ATM strike price (replace with actual implementation)"""
        # Mock implementation - in real scenario, get from market data
        base_strike = 23500  # Base Nifty level
        date_hash = hash(self.market.current_date) % 1000
        return base_strike + (date_hash - 500)  # Add some variation
    
    def _find_option_symbols(self, atm_strike: int, sell_price: float, buy_price: float) -> Optional[Dict]:
        """Find option symbols based on target prices (mock implementation)"""
        try:
            # Mock option symbol generation
            # In real implementation, search through available option chain data
            
            # Generate mock symbols around ATM
            ce_strikes = [atm_strike + i * 50 for i in range(-10, 11)]
            pe_strikes = [atm_strike + i * 50 for i in range(-10, 11)]
            
            # Find closest strikes for selling (around target price)
            sell_ce_strike = min(ce_strikes, key=lambda x: abs(x - atm_strike - 200))  # Slightly OTM
            sell_pe_strike = max(pe_strikes, key=lambda x: abs(x - atm_strike + 200))  # Slightly OTM
            
            # Find strikes for buying (further OTM)
            buy_ce_strike = sell_ce_strike + 200
            buy_pe_strike = sell_pe_strike - 200
            
            # Generate symbol names (adjust format based on your data)
            current_date = datetime.strptime(self.market.current_date, "%Y%m%d")
            expiry_date = current_date.strftime("%y%m%d")  # Simplified expiry
            
            symbols = {
                'sell_ce': f"NSE:NIFTY{expiry_date}{sell_ce_strike}CE",
                'sell_pe': f"NSE:NIFTY{expiry_date}{sell_pe_strike}PE",
                'buy_ce': f"NSE:NIFTY{expiry_date}{buy_ce_strike}CE",
                'buy_pe': f"NSE:NIFTY{expiry_date}{buy_pe_strike}PE"
            }
            
            logger.info(f"Selected option symbols: {symbols}")
            return symbols
            
        except Exception as e:
            logger.error(f"Error finding option symbols: {e}")
            return None
    
    def check_delta_neutrality(self) -> bool:
        """Check if delta adjustment is needed"""
        try:
            positions = self.portfolio.get_strategy_positions()
            
            ce_short = positions.get('CE_SHORT')
            pe_short = positions.get('PE_SHORT')
            
            if not ce_short or not pe_short:
                logger.warning("Missing CE or PE short positions for delta check")
                return False
            
            ce_price = ce_short['ltp']
            pe_price = pe_short['ltp']
            
            # Calculate ratio of lower LTP to higher LTP
            lower_ltp = min(ce_price, pe_price)
            higher_ltp = max(ce_price, pe_price)
            ratio = round(lower_ltp / higher_ltp, 2) if higher_ltp != 0 else 0
            
            # Get threshold based on VIX
            threshold = self.config.get_threshold(self.vix_value)
            
            logger.info(f"Delta check - Ratio: {ratio}, Threshold: {threshold}")
            
            return ratio < threshold
            
        except Exception as e:
            logger.error(f"Error in delta check: {e}")
            return False
    
    def check_atm_condition(self) -> bool:
        """Check if any short leg has become ATM or ITM"""
        try:
            positions = self.portfolio.get_strategy_positions()
            
            ce_short = positions.get('CE_SHORT')
            pe_short = positions.get('PE_SHORT')
            
            if not ce_short or not pe_short:
                return False
            
            # Extract strike prices from symbols
            ce_strike = self._extract_strike_from_symbol(ce_short['symbol'])
            pe_strike = self._extract_strike_from_symbol(pe_short['symbol'])
            
            if not ce_strike or not pe_strike:
                logger.warning("Could not extract strike prices from symbols")
                return False
            
            # Get current ATM strike
            current_atm = self._get_mock_atm_strike()
            
            # Check if either leg is ATM or ITM
            ce_atm_itm = ce_strike <= current_atm
            pe_atm_itm = pe_strike >= current_atm
            
            logger.info(f"ATM check - CE strike: {ce_strike}, PE strike: {pe_strike}, ATM: {current_atm}")
            
            return ce_atm_itm or pe_atm_itm
            
        except Exception as e:
            logger.error(f"Error in ATM condition check: {e}")
            return False
    
    def _extract_strike_from_symbol(self, symbol: str) -> Optional[int]:
        """Extract strike price from option symbol"""
        try:
            # Extract 5-digit strike price from symbol
            if 'CE' in symbol:
                match = re.search(r'(\d{5})CE', symbol)
            elif 'PE' in symbol:
                match = re.search(r'(\d{5})PE', symbol)
            else:
                return None
            
            if match:
                return int(match.group(1))
            return None
            
        except Exception as e:
            logger.error(f"Error extracting strike from {symbol}: {e}")
            return None
    
    def execute_delta_action(self) -> bool:
        """Execute delta adjustment action"""
        try:
            # Check if ATM/ITM condition is met
            if self.check_atm_condition():
                logger.info("ATM/ITM condition met - squaring off and recreating strategy")
                return self._square_off_and_recreate()
            else:
                logger.info("Delta adjustment needed but not ATM/ITM - performing adjustment")
                return self._perform_delta_adjustment()
                
        except Exception as e:
            logger.error(f"Error in delta action: {e}")
            return False
    
    def _square_off_and_recreate(self) -> bool:
        """Square off all positions and recreate strategy"""
        try:
            current_time = self.market.get_current_time()
            
            # Get current market data for all positions
            position_symbols = list(self.portfolio.positions.keys())
            market_data = self.market.get_current_prices(position_symbols)
            
            # Square off all positions
            self.portfolio.square_off_all_positions(current_time, market_data)
            
            # Recreate strategy
            return self.execute_bcs_strategy()
            
        except Exception as e:
            logger.error(f"Error in square off and recreate: {e}")
            return False
    
    def _perform_delta_adjustment(self) -> bool:
        """Perform delta adjustment by closing profitable leg"""
        try:
            positions = self.portfolio.get_strategy_positions()
            
            ce_short = positions.get('CE_SHORT')
            pe_short = positions.get('PE_SHORT')
            
            if not ce_short or not pe_short:
                return False
            
            # Determine which leg to close (lowest priced)
            if ce_short['ltp'] < pe_short['ltp']:
                leg_to_close = 'CE'
                close_symbols = [ce_short['symbol'], positions.get('CE_BUY', {}).get('symbol')]
            else:
                leg_to_close = 'PE'
                close_symbols = [pe_short['symbol'], positions.get('PE_BUY', {}).get('symbol')]
            
            # Close the selected leg
            current_time = self.market.get_current_time()
            for symbol in close_symbols:
                if symbol and symbol in self.portfolio.positions:
                    market_data = self.market.get_current_prices([symbol])
                    if symbol in market_data:
                        exit_price = market_data[symbol]['ltp']
                        self.portfolio.close_position(symbol, exit_price, current_time, "delta_adjustment")
            
            # Create new positions for the closed leg (simplified)
            # In real implementation, find new strikes based on current market conditions
            logger.info(f"Delta adjustment completed for {leg_to_close} leg")
            return True
            
        except Exception as e:
            logger.error(f"Error in delta adjustment: {e}")
            return False
    
    def check_exit_conditions(self) -> Tuple[bool, str]:
        """Check if any exit conditions are met"""
        try:
            # Check time-based exit
            current_time = self.market.get_current_time()
            if current_time and current_time.time() >= self.config.exit_time:
                return True, "time_exit"
            
            # Check risk limits
            risk_status = self.portfolio.check_risk_limits()
            
            if risk_status['max_loss_breached']:
                return True, "max_loss"
            
            if risk_status['max_profit_reached']:
                return True, "max_profit"
            
            if risk_status['drawdown_limit']:
                return True, "drawdown_limit"
            
            return False, "no_exit"
            
        except Exception as e:
            logger.error(f"Error checking exit conditions: {e}")
            return False, "error"
    
    def process_market_update(self, market_data: Dict):
        """Process market data update and execute strategy logic"""
        try:
            # Update portfolio with new prices
            self.portfolio.update_positions(market_data)
            
            # Check exit conditions first
            should_exit, exit_reason = self.check_exit_conditions()
            if should_exit:
                logger.info(f"Exit condition met: {exit_reason}")
                return {'action': 'exit', 'reason': exit_reason}
            
            # Check delta neutrality
            current_time = self.market.get_current_time()
            if (not self.last_delta_check_time or 
                (current_time - self.last_delta_check_time).total_seconds() >= self.config.delta_check_interval):
                
                if self.check_delta_neutrality():
                    logger.info("Delta condition met - executing delta action")
                    self.execute_delta_action()
                    self.last_delta_check_time = current_time
                    return {'action': 'delta_adjustment', 'reason': 'delta_condition_met'}
                
                self.last_delta_check_time = current_time
            
            return {'action': 'continue', 'reason': 'normal_operation'}
            
        except Exception as e:
            logger.error(f"Error processing market update: {e}")
            return {'action': 'error', 'reason': str(e)}
