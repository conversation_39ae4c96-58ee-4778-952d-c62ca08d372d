
def get_half_qty(qty):
    """
    This function returns:
    - Half of the quantity if it's even.
    - Half of the quantity rounded down to the nearest lower multiple of 75 if it's odd.
    """
    if qty % 2 == 0:  # If the quantity is even
        return qty // 2  # Return half of the quantity (integer division)
    else:
        # If the quantity is odd, divide by 2 and round down to the nearest multiple of 75
        half_qty = qty // 2
        return (half_qty // 75) * 75  # Round down to the nearest lower multiple of 75

#--------------------------------------------------------------------------------------------------
# Example usage:
#qty = 225
#print(f"Half of {qty} is {get_half_qty(qty)}")  # Should return 75
