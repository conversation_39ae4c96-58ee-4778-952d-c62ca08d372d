"""
Main Backtesting Runner
Orchestrates the complete backtesting process
"""

import logging
import sys
from datetime import datetime, timedelta
from typing import Dict, List, Optional
from pathlib import Path

from .config import BacktestConfig
from .data_loader import DataLoader
from .market_simulator import MarketDataSimulator
from .portfolio_manager import PortfolioManager
from .strategy_backtester import StrategyBacktester
from .results_analyzer import ResultsAnalyzer

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('backtesting.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)


class BacktestRunner:
    """Main class to run the complete backtesting process"""
    
    def __init__(self, config: BacktestConfig = None):
        self.config = config or BacktestConfig.from_env()
        
        # Initialize components
        self.data_loader = DataLoader(self.config)
        self.market_simulator = MarketDataSimulator(self.config, self.data_loader)
        self.portfolio_manager = PortfolioManager(self.config)
        self.strategy_backtester = StrategyBacktester(
            self.config, self.portfolio_manager, self.market_simulator
        )
        self.results_analyzer = ResultsAnalyzer(self.config)
        
        # Tracking variables
        self.portfolio_history = []
        self.daily_summaries = []
        self.current_date = None
        
    def run_backtest(self) -> Dict:
        """Run the complete backtesting process"""
        try:
            logger.info("Starting backtesting process...")
            logger.info(f"Configuration: {self.config.to_dict()}")
            
            # Get available trading dates
            available_dates = self.data_loader.get_available_dates()
            
            if not available_dates:
                logger.error("No trading dates found in data directory")
                return {}
            
            logger.info(f"Found {len(available_dates)} trading dates: {available_dates[0]} to {available_dates[-1]}")
            
            # Run backtest for each trading day
            for date_str in available_dates:
                logger.info(f"\n{'='*60}")
                logger.info(f"Processing trading day: {date_str}")
                logger.info(f"{'='*60}")
                
                success = self._run_single_day(date_str)
                
                if not success:
                    logger.warning(f"Failed to process {date_str}, continuing with next day...")
                    continue
                
                # Record daily summary
                self._record_daily_summary(date_str)
                
                # Reset portfolio for next day (if needed)
                self._prepare_for_next_day()
            
            # Analyze results
            logger.info("\nAnalyzing backtest results...")
            results = self.results_analyzer.analyze_backtest_results(
                self.portfolio_history,
                self.portfolio_manager.completed_trades,
                self.daily_summaries
            )
            
            # Print summary
            self.results_analyzer.print_summary_report(results)
            
            logger.info("Backtesting completed successfully!")
            return results
            
        except Exception as e:
            logger.error(f"Error in backtesting process: {e}")
            return {}
    
    def _run_single_day(self, date_str: str) -> bool:
        """Run backtesting for a single trading day"""
        try:
            self.current_date = date_str
            
            # Initialize market data for the day
            if not self.market_simulator.initialize_day(date_str):
                logger.error(f"Failed to initialize market data for {date_str}")
                return False
            
            # Initialize strategy
            if not self.strategy_backtester.initialize_strategy(date_str):
                logger.error(f"Failed to initialize strategy for {date_str}")
                return False
            
            # Main trading loop
            iteration_count = 0
            max_iterations = 1000  # Safety limit
            
            while self.market_simulator.is_market_open and iteration_count < max_iterations:
                iteration_count += 1
                
                # Get current market data
                position_symbols = list(self.portfolio_manager.positions.keys())
                if position_symbols:
                    market_data = self.market_simulator.get_current_prices(position_symbols)
                    
                    # Process market update through strategy
                    strategy_action = self.strategy_backtester.process_market_update(market_data)
                    
                    # Handle strategy actions
                    if strategy_action['action'] == 'exit':
                        logger.info(f"Exit signal received: {strategy_action['reason']}")
                        self._handle_exit(market_data)
                        break
                    elif strategy_action['action'] == 'error':
                        logger.error(f"Strategy error: {strategy_action['reason']}")
                        break
                
                # Record portfolio state
                self._record_portfolio_state()
                
                # Advance market time
                if not self.market_simulator.advance_time():
                    logger.info("Market closed for the day")
                    break
                
                # Small delay to prevent excessive logging
                if iteration_count % 100 == 0:
                    logger.info(f"Processed {iteration_count} market updates for {date_str}")
            
            # End of day cleanup
            self._end_of_day_cleanup()
            
            logger.info(f"Completed trading day {date_str} after {iteration_count} iterations")
            return True
            
        except Exception as e:
            logger.error(f"Error processing day {date_str}: {e}")
            return False
    
    def _handle_exit(self, market_data: Dict):
        """Handle exit conditions"""
        try:
            current_time = self.market_simulator.get_current_time()
            
            # Square off all positions
            self.portfolio_manager.square_off_all_positions(current_time, market_data)
            
            logger.info("All positions squared off due to exit condition")
            
        except Exception as e:
            logger.error(f"Error handling exit: {e}")
    
    def _record_portfolio_state(self):
        """Record current portfolio state for analysis"""
        try:
            portfolio_summary = self.portfolio_manager.get_portfolio_summary()
            portfolio_summary['timestamp'] = self.market_simulator.get_current_time()
            portfolio_summary['date'] = self.current_date
            
            self.portfolio_history.append(portfolio_summary)
            
        except Exception as e:
            logger.error(f"Error recording portfolio state: {e}")
    
    def _record_daily_summary(self, date_str: str):
        """Record daily summary statistics"""
        try:
            portfolio_summary = self.portfolio_manager.get_portfolio_summary()
            
            # Calculate daily metrics
            daily_pnl = portfolio_summary['total_pnl']
            daily_pnl_pct = (daily_pnl / self.config.initial_capital) * 100
            
            daily_summary = {
                'date': date_str,
                'daily_pnl': daily_pnl,
                'daily_pnl_pct': daily_pnl_pct,
                'realized_pnl': portfolio_summary['realized_pnl'],
                'unrealized_pnl': portfolio_summary['unrealized_pnl'],
                'total_commission': portfolio_summary['total_commission'],
                'trades_count': len([t for t in self.portfolio_manager.completed_trades 
                                   if t.entry_time.strftime('%Y%m%d') == date_str]),
                'max_drawdown': portfolio_summary['max_drawdown'],
                'end_capital': portfolio_summary['current_capital']
            }
            
            self.daily_summaries.append(daily_summary)
            
            logger.info(f"Daily Summary for {date_str}:")
            logger.info(f"  Daily PnL: ${daily_pnl:.2f} ({daily_pnl_pct:.2f}%)")
            logger.info(f"  End Capital: ${daily_summary['end_capital']:,.2f}")
            logger.info(f"  Trades: {daily_summary['trades_count']}")
            
        except Exception as e:
            logger.error(f"Error recording daily summary: {e}")
    
    def _end_of_day_cleanup(self):
        """Perform end of day cleanup"""
        try:
            # Ensure all positions are closed
            current_time = self.market_simulator.get_current_time()
            if self.portfolio_manager.positions:
                logger.warning(f"Found {len(self.portfolio_manager.positions)} open positions at EOD")
                
                # Get final prices for remaining positions
                position_symbols = list(self.portfolio_manager.positions.keys())
                final_market_data = self.market_simulator.get_current_prices(position_symbols)
                
                # Force close all positions
                self.portfolio_manager.square_off_all_positions(current_time, final_market_data)
            
            # Close market
            self.market_simulator.close_market()
            
        except Exception as e:
            logger.error(f"Error in end of day cleanup: {e}")
    
    def _prepare_for_next_day(self):
        """Prepare for next trading day"""
        try:
            # Reset daily PnL tracking
            self.portfolio_manager.reset_daily_pnl()
            
            # Clear strategy state
            self.strategy_backtester.strategy_initialized = False
            self.strategy_backtester.last_delta_check_time = None
            
            # Clear data cache if needed (to manage memory)
            if len(self.data_loader.data_cache) > 10:  # Keep last 10 days in cache
                self.data_loader.clear_cache()
            
        except Exception as e:
            logger.error(f"Error preparing for next day: {e}")
    
    def run_quick_test(self, test_date: str = None) -> Dict:
        """Run a quick test on a single day for debugging"""
        try:
            if test_date:
                self.config.start_date = test_date
                self.config.end_date = test_date
            
            logger.info(f"Running quick test for {test_date or 'default date'}")
            
            # Get available dates
            available_dates = self.data_loader.get_available_dates()
            
            if not available_dates:
                logger.error("No data available for quick test")
                return {}
            
            test_date = available_dates[0] if not test_date else test_date
            
            # Run single day
            success = self._run_single_day(test_date)
            
            if success:
                self._record_daily_summary(test_date)
                
                # Quick analysis
                results = self.results_analyzer.analyze_backtest_results(
                    self.portfolio_history,
                    self.portfolio_manager.completed_trades,
                    self.daily_summaries
                )
                
                self.results_analyzer.print_summary_report(results)
                return results
            else:
                logger.error("Quick test failed")
                return {}
                
        except Exception as e:
            logger.error(f"Error in quick test: {e}")
            return {}


def main():
    """Main entry point for backtesting"""
    try:
        # Create configuration
        config = BacktestConfig.from_env()
        
        # Create and run backtester
        runner = BacktestRunner(config)
        results = runner.run_backtest()
        
        if results:
            print("\nBacktesting completed successfully!")
            print(f"Results saved to: {config.results_dir}")
        else:
            print("Backtesting failed!")
            
    except Exception as e:
        logger.error(f"Error in main: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
