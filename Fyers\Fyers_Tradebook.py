from fyers_apiv3 import fyersModel
import pickle
import os
import logging
import pandas as pd
from Fyers import Fyers_Utility 

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

tradebook = []

def load_tradebook_from_pickle():
    """
    Load the tradebook from the pickle file.
    """
    global tradebook
    try:
        with open("tradebook.pickle", "rb") as f:
            tradebook = pickle.load(f)
            logging.info("Tradebook loaded successfully from pickle file.")
    except FileNotFoundError:
        tradebook = []
        logging.warning("Pickle file not found. Initialized empty tradebook.")

    return

def save_tradebook_to_pickle():
    """
    Save the tradebook to a pickle file.
    """
    global tradebook
    with open("tradebook.pickle", "wb") as f:
        pickle.dump(tradebook, f)
    logging.info("Tradebook saved to pickle file.")
    return

def get_tradebook():
    global tradebook
    return tradebook

def tradebookinitialize():
    load_tradebook_from_pickle()
    logging.info("Tradebook initialized.")

    return

def update_tradebook(tradeslist):
    global tradebook
    for trade in tradeslist:
        if(trade not in tradebook):
            tradebook.append(trade)
            logging.info(f"Trade added: {trade}")

    return

def update_tradebook_from_trades(fyers):
    trades_data = fyers.tradebook()
    #print(trades_data)
    if trades_data is None or "tradeBook" not in trades_data:
            return {"error": "Failed to retrieve trades data or unexpected format."}
    tradebook = get_tradebook()
    #print(tradebook)

    updateflag = False
    for trade in trades_data["tradeBook"]:
        if trade not in tradebook:
            tradebook.append(trade)
            updateflag = True    
            logging.info(f"New trade added from Fyers: {trade}")

    if(updateflag):
            update_tradebook(tradebook)
            logging.info("Tradebook updated with new trades from Fyers.")

#Example Usage
#fyers = initialize_fyers()
#update_tradebook_from_trades(fyers) 
#save_tradebook_to_pickle()      