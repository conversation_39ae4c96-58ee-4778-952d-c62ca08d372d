import config 
import os
from Symphony import Symphony_To_Fyers_Converter 
from Symphony import Symphony_Utility
import logger_config


logger = logger_config.get_logger('main_logger')

broker = config.BrokerContext()
def thursday_get_positions(broker, clientID=os.getenv('Interactive_clientID')):
    """
    Fetch all open positions from Fyers API and segregate them into CE, PE as well as Buy and Short side.
    :return: Dictionary containing segregated positions with only symbol, ltp, and side.
    """
    try:
        # Force reinitialize Symphony connection
        broker.interactive_symphony = Symphony_Utility.initialize_interactive_symphony()

        response = broker.interactive_symphony.get_position_netwise(clientID=clientID)
        if response.get("type") == "success":
            positions = response.get("result", {}).get("positionList", [])
                
            # Segregation dictionaries
            ce_buy = []
            ce_short = {}
            pe_buy = []
            pe_short = {}

            for position in positions:
                # Assuming 'quantity' is a key that indicates if the position is open
                if int(position.get("Quantity", 0)) != 0 and position.get("ProductType", "") == "NRML" and "NIFTY" in position.get("TradingSymbol", "") and "BANKNIFTY" not in position.get("TradingSymbol", "") and "CNC" not in position.get("TradingSymbol", ""):

                    symbol = position.get("TradingSymbol", "")
                    qty = int(position.get("Quantity", 0))
                    buy_avg_price = position.get("BuyAveragePrice", 0)
                    sell_avg_price = position.get("SellAveragePrice", 0)
                    side = "BUY" if qty > 0 else "SHORT"
                    ltp = buy_avg_price if side == "BUY" else sell_avg_price  # Use BuyAvgPrice for Buy, SellAvgPrice for Short
                    pnl = position.get("NetAmount", 0)

                    # Convert symbol to Fyers format
                    fyers_symbol = Symphony_To_Fyers_Converter.convert_to_fyers_symbol(int(position.get("ExchangeInstrumentId")))
                    
                    # Classify as CE or PE and Buy or Short
                    if "CE" in symbol:
                        if side == "BUY":
                            ce_buy.append({"symbol": fyers_symbol, "ltp": ltp, "side": side, "qty": abs(qty)})
                        else:
                            ce_short = {"symbol": fyers_symbol, "ltp": ltp, "side": side, "qty": abs(qty)}
                    elif "PE" in symbol:
                        if side == "BUY":
                            pe_buy.append({"symbol": fyers_symbol, "ltp": ltp, "side": side, "qty":abs(qty)})
                        else:
                            pe_short = {"symbol": fyers_symbol, "ltp": ltp, "side": side, "qty":abs(qty)}
            
            # Check for at least 4 buy positions
            total_buy_positions = len(ce_buy) + len(pe_buy)
            if total_buy_positions < 4:
                logger.warning(f"Not enough buy positions. Found {total_buy_positions}, but at least 4 required.") 
          
            #Print the segregated positions
            logger.info(f"CE_BUY:{ce_buy}")
            logger.info(f"CE_SHORT:{ce_short}")
            logger.info(f"PE_BUY:{pe_buy}")
            logger.info(f"PE_SHORT:{pe_short}")

            return {
                "CE_BUY": ce_buy,
                "CE_SHORT": ce_short,
                "PE_BUY": pe_buy,
                "PE_SHORT": pe_short,

            }
    except Exception as e:
        return {"error": str(e)}

#broker = BrokerContext()
#thursday_get_positions(broker, clientID=os.getenv('Interactive_clientID'))