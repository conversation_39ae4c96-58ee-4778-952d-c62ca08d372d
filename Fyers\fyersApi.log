{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-19 10:11:54,213+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-19 10:17:36,000+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-19 10:17:48,131+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-19 10:39:47,795+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:02,382+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:02,962+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:03,519+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:04,102+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:04,719+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:06,326+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:06,965+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:07,552+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:08,154+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:08,764+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:17,319+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:17,905+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:18,486+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:19,051+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:19,631+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:21,343+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:21,993+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:22,611+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:23,210+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:23,834+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:32,420+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:33,056+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:33,660+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:34,257+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:34,848+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:36,471+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:37,091+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:37,709+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:38,314+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:38,905+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:47,477+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:48,066+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:48,665+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:49,285+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:49,927+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:51,533+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:52,133+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:52,722+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:53,406+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:40:54,206+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-19 10:52:28,928+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:04,072+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:05,247+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:05,876+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:06,559+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:07,361+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:09,093+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:09,789+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:10,467+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:11,098+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:11,788+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:17,537+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:18,290+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:19,053+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:19,709+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:20,479+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:22,188+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:22,854+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:23,519+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:24,133+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:24,837+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:32,435+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:33,041+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:33,622+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:34,273+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:34,928+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:36,523+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:37,120+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:37,717+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:38,308+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:38,920+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:47,570+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:48,177+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:48,785+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:49,414+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:50,010+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:51,694+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:52,385+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:53,008+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:53,605+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-19 10:55:54,218+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-19 11:05:33,091+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-19 11:10:36,096+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-19 11:24:51,234+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-19 11:34:32,862+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-19 14:03:37,636+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/quotes","Error":{"message":"Please provide valid token","code":-15,"s":"error"}},"timestamp":"2025-02-20 10:50:42,195+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/quotes","Error":{"message":"Please provide valid token","code":-15,"s":"error"}},"timestamp":"2025-02-20 10:52:25,621+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-20 11:54:59,615+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-20 12:00:28,407+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-20 12:02:38,624+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-20 12:14:37,401+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-20 12:20:00,325+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-20 12:21:44,931+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-20 12:23:33,942+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-20 12:30:23,859+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-20 12:35:57,064+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-20 12:57:23,300+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-20 13:01:36,629+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-20 13:01:50,956+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-20 13:04:34,420+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-20 13:06:07,145+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-20 13:09:31,496+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-20 13:11:04,674+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-20 13:14:52,768+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-20 13:15:16,891+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-20 13:18:02,723+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-21 10:00:50,722+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-21 11:00:14,646+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-21 12:18:26,624+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-21 14:54:49,911+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-21 15:02:47,003+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/tradebook","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:12:42,209+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:12:42,537+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:12:46,553+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:13:00,907+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:13:16,270+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:13:30,604+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:13:45,917+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:14:01,231+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:14:15,620+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:14:30,983+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:14:46,309+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:15:00,674+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:15:16,012+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:15:30,353+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:15:45,692+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:16:01,007+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:16:15,552+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:16:30,889+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:16:46,236+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:17:00,554+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:17:15,885+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:17:31,406+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:17:45,704+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:18:01,044+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-24 10:18:15,565+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/quotes","Error":{"message":"Please provide valid token","code":-15,"s":"error"}},"timestamp":"2025-02-25 10:27:49,669+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 12:08:01,982+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 12:09:11,969+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 12:10:23,469+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 12:19:52,411+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 12:20:04,507+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 12:21:20,248+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 12:25:39,584+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[post_call:86] fyersModel","message":{"API":"/orders/sync","Error":{"code":-50,"message":"Invalid input","s":"error"}},"timestamp":"2025-02-25 13:12:45,976+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 13:24:26,775+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/tradebook","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 15:02:02,850+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 15:02:03,169+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/tradebook","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 15:02:53,918+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 15:02:54,211+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/tradebook","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 15:03:35,225+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 15:03:35,516+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/tradebook","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 15:04:34,050+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 15:04:34,342+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/tradebook","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 15:04:49,469+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 15:04:49,774+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/tradebook","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 15:05:27,813+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-25 15:05:32,369+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-26 09:07:20,891+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/tradebook","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-26 09:25:33,286+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-26 09:25:33,586+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-26 09:25:33,889+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-26 09:30:58,228+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/quotes","Error":{"message":"Please provide valid token","code":-15,"s":"error"}},"timestamp":"2025-02-27 09:19:57,759+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-27 09:20:31,043+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-27 09:20:31,518+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-27 09:20:45,818+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-02-27 09:20:46,116+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/quotes","Error":{"message":"Please provide valid token","code":-15,"s":"error"}},"timestamp":"2025-02-27 09:21:16,091+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-03-04 08:39:49,937+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/quotes","Error":{"message":"Please provide valid token","code":-15,"s":"error"}},"timestamp":"2025-03-13 10:31:08,870+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/quotes","Error":{"message":"Please provide valid token","code":-15,"s":"error"}},"timestamp":"2025-03-13 10:43:15,450+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/quotes","Error":{"message":"Please provide valid token","code":-15,"s":"error"}},"timestamp":"2025-03-13 10:43:50,993+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-03-17 09:51:20,407+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-03-17 09:53:40,599+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/quotes","Error":{"message":"Please provide valid token","code":-15,"s":"error"}},"timestamp":"2025-03-17 09:54:23,128+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-03-18 09:54:01,748+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-03-18 09:54:02,620+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-03-18 09:54:16,435+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-03-18 09:54:16,964+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-03-18 09:54:31,633+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-03-18 09:54:32,097+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-03-18 09:54:45,667+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-03-18 09:54:46,067+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/quotes","Error":{"message":"Please provide valid token","code":-15,"s":"error"}},"timestamp":"2025-03-21 10:45:24,316+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/quotes","Error":{"message":"Please provide valid token","code":-15,"s":"error"}},"timestamp":"2025-03-21 10:46:55,372+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/quotes","Error":{"message":"Please provide valid token","code":-15,"s":"error"}},"timestamp":"2025-03-24 11:23:24,807+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/quotes","Error":{"message":"Please provide valid token","code":-15,"s":"error"}},"timestamp":"2025-03-24 11:25:14,417+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/quotes","Error":{"message":"Please provide valid token","code":-15,"s":"error"}},"timestamp":"2025-03-24 11:26:35,942+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-03-24 11:28:47,254+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-03-24 11:28:47,553+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-03-24 11:29:16,858+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-03-24 11:29:17,197+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/quotes","Error":{"message":"Please provide valid token","code":-15,"s":"error"}},"timestamp":"2025-03-24 11:29:34,814+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-03-24 12:22:16,516+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-03-24 12:22:16,803+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-03-24 12:22:32,107+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-03-24 12:22:32,410+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/quotes","Error":{"message":"Please provide valid token","code":-15,"s":"error"}},"timestamp":"2025-03-26 10:12:29,830+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/quotes","Error":{"message":"Please provide valid token","code":-15,"s":"error"}},"timestamp":"2025-03-26 10:12:30,935+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/quotes","Error":{"message":"Please provide valid token","code":-15,"s":"error"}},"timestamp":"2025-03-26 10:14:25,304+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-04-01 09:43:34,723+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-04-01 09:49:06,181+0530","service":"FyersAPI"}
{"level":"ERROR","location":"[get_call:143] fyersModel","message":{"API":"/positions","Error":{"code":-16,"message":"Could not authenticate the user","s":"error"}},"timestamp":"2025-04-01 10:00:01,058+0530","service":"FyersAPI"}
