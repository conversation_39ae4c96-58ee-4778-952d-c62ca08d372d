{"pagination": {"DescribeAutoScalingGroups": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxRecords", "result_key": "AutoScalingGroups"}, "DescribeAutoScalingInstances": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxRecords", "result_key": "AutoScalingInstances"}, "DescribeLaunchConfigurations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxRecords", "result_key": "LaunchConfigurations"}, "DescribeNotificationConfigurations": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxRecords", "result_key": "NotificationConfigurations"}, "DescribePolicies": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxRecords", "result_key": "ScalingPolicies"}, "DescribeScalingActivities": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxRecords", "result_key": "Activities"}, "DescribeScheduledActions": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxRecords", "result_key": "ScheduledUpdateGroupActions"}, "DescribeTags": {"input_token": "NextToken", "output_token": "NextToken", "limit_key": "MaxRecords", "result_key": "Tags"}, "DescribeLoadBalancerTargetGroups": {"input_token": "NextToken", "limit_key": "MaxRecords", "output_token": "NextToken", "result_key": "LoadBalancerTargetGroups"}, "DescribeLoadBalancers": {"input_token": "NextToken", "limit_key": "MaxRecords", "output_token": "NextToken", "result_key": "LoadBalancers"}, "DescribeWarmPool": {"input_token": "NextToken", "limit_key": "MaxRecords", "output_token": "NextToken", "result_key": "Instances"}}}