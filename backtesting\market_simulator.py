"""
Market Data Simulator Mo<PERSON>le
Simulates real-time market data feed for backtesting
"""

import pandas as pd
import numpy as np
from datetime import datetime, time, timedelta
from typing import Dict, List, Optional, Callable, Any
import logging
from .config import BacktestConfig
from .data_loader import DataLoader

logger = logging.getLogger(__name__)


class MarketDataSimulator:
    """Simulates market data feed for backtesting"""
    
    def __init__(self, config: BacktestConfig, data_loader: DataLoader):
        self.config = config
        self.data_loader = data_loader
        self.current_time = None
        self.current_date = None
        self.market_data = {}
        self.subscribers = []
        self.is_market_open = False
        
    def add_subscriber(self, callback: Callable[[Dict], None]):
        """Add a callback function to receive market data updates"""
        self.subscribers.append(callback)
    
    def remove_subscriber(self, callback: Callable[[Dict], None]):
        """Remove a subscriber callback"""
        if callback in self.subscribers:
            self.subscribers.remove(callback)
    
    def _notify_subscribers(self, market_data: Dict):
        """Notify all subscribers of market data update"""
        for callback in self.subscribers:
            try:
                callback(market_data)
            except Exception as e:
                logger.error(f"Error notifying subscriber: {e}")
    
    def initialize_day(self, date_str: str) -> bool:
        """Initialize market data for a trading day"""
        try:
            self.current_date = date_str
            self.current_time = datetime.combine(
                datetime.strptime(date_str, "%Y%m%d").date(),
                self.config.start_time
            )
            
            # Load all option data for the day
            day_data = self.data_loader.load_day_data(date_str)
            
            if not day_data:
                logger.warning(f"No data available for {date_str}")
                return False
            
            # Prepare market data structure
            self.market_data = {}
            for option_type, data in day_data.items():
                if data is not None and not data.empty:
                    # Group by symbol for efficient lookup
                    self.market_data[option_type] = data.groupby('symbol')
            
            self.is_market_open = True
            logger.info(f"Market data initialized for {date_str}")
            return True
            
        except Exception as e:
            logger.error(f"Error initializing day data for {date_str}: {e}")
            return False
    
    def get_current_prices(self, symbols: List[str]) -> Dict[str, Dict]:
        """Get current market prices for given symbols"""
        if not self.is_market_open:
            return {}
        
        current_prices = {}
        current_timestamp = self.current_time
        
        for symbol in symbols:
            price_data = self._get_symbol_price_at_time(symbol, current_timestamp)
            if price_data:
                current_prices[symbol] = price_data
        
        return current_prices
    
    def _get_symbol_price_at_time(self, symbol: str, timestamp: datetime) -> Optional[Dict]:
        """Get price data for a symbol at a specific timestamp"""
        # Find which option type this symbol belongs to
        option_type = self._identify_option_type(symbol)
        
        if not option_type or option_type not in self.market_data:
            return None
        
        try:
            symbol_group = self.market_data[option_type].get_group(symbol)
            
            # Find the closest timestamp <= current timestamp
            valid_data = symbol_group[symbol_group['timestamp'] <= timestamp]
            
            if valid_data.empty:
                return None
            
            # Get the latest available data point
            latest_data = valid_data.iloc[-1]
            
            return {
                'symbol': symbol,
                'ltp': float(latest_data.get('ltp', 0)),
                'bid': float(latest_data.get('bid', 0)),
                'ask': float(latest_data.get('ask', 0)),
                'volume': int(latest_data.get('volume', 0)),
                'oi': int(latest_data.get('oi', 0)),
                'timestamp': latest_data['timestamp']
            }
            
        except KeyError:
            # Symbol not found in this option type
            return None
        except Exception as e:
            logger.error(f"Error getting price for {symbol}: {e}")
            return None
    
    def _identify_option_type(self, symbol: str) -> Optional[str]:
        """Identify which option type a symbol belongs to"""
        # This is a simplified approach - you might need to adjust based on your symbol naming convention
        if 'CE' in symbol:
            return 'CE_SELL' if self._is_sell_symbol(symbol) else 'CE_BUY'
        elif 'PE' in symbol:
            return 'PE_SELL' if self._is_sell_symbol(symbol) else 'PE_BUY'
        return None
    
    def _is_sell_symbol(self, symbol: str) -> bool:
        """Determine if a symbol is typically used for selling (simplified logic)"""
        # This is a placeholder - implement based on your symbol classification logic
        # For now, assume all symbols can be both bought and sold
        return True
    
    def advance_time(self, seconds: int = None) -> bool:
        """Advance market time by specified seconds or to next data point"""
        if not self.is_market_open:
            return False
        
        if seconds is None:
            seconds = self.config.delta_check_interval
        
        self.current_time += timedelta(seconds=seconds)
        
        # Check if market should close
        market_close_time = datetime.combine(
            self.current_time.date(),
            self.config.exit_time
        )
        
        if self.current_time >= market_close_time:
            self.is_market_open = False
            logger.info(f"Market closed at {self.current_time}")
            return False
        
        return True
    
    def get_current_time(self) -> Optional[datetime]:
        """Get current market time"""
        return self.current_time
    
    def is_market_hours(self) -> bool:
        """Check if current time is within market hours"""
        if not self.current_time:
            return False
        
        current_time_only = self.current_time.time()
        return (self.config.start_time <= current_time_only <= self.config.exit_time)
    
    def get_vix_value(self) -> float:
        """Get current VIX value"""
        if self.current_date:
            return self.data_loader.get_vix_data(self.current_date)
        return 15.0  # Default VIX
    
    def simulate_order_execution(self, symbol: str, side: str, quantity: int, 
                                order_type: str = "market") -> Dict:
        """Simulate order execution and return execution details"""
        current_price_data = self._get_symbol_price_at_time(symbol, self.current_time)
        
        if not current_price_data:
            return {
                'status': 'rejected',
                'reason': 'No market data available',
                'symbol': symbol,
                'side': side,
                'quantity': quantity
            }
        
        # Calculate execution price with slippage
        ltp = current_price_data['ltp']
        bid = current_price_data.get('bid', ltp)
        ask = current_price_data.get('ask', ltp)
        
        if order_type == "market":
            if side.lower() == "buy":
                execution_price = ask if ask > 0 else ltp
            else:  # sell
                execution_price = bid if bid > 0 else ltp
        else:  # limit order - simplified to use LTP
            execution_price = ltp
        
        # Apply slippage
        slippage_factor = self.config.slippage_bps / 10000.0
        if side.lower() == "buy":
            execution_price *= (1 + slippage_factor)
        else:
            execution_price *= (1 - slippage_factor)
        
        return {
            'status': 'executed',
            'symbol': symbol,
            'side': side,
            'quantity': quantity,
            'execution_price': round(execution_price, 2),
            'execution_time': self.current_time,
            'commission': self.config.commission_per_trade
        }
    
    def close_market(self):
        """Close the market for the day"""
        self.is_market_open = False
        self.market_data.clear()
        logger.info(f"Market closed for {self.current_date}")
    
    def get_market_status(self) -> Dict:
        """Get current market status"""
        return {
            'is_open': self.is_market_open,
            'current_time': self.current_time,
            'current_date': self.current_date,
            'market_hours': self.is_market_hours(),
            'vix': self.get_vix_value()
        }
