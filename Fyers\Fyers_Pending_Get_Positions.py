from datetime import datetime
from fyers_apiv3 import fyersModel
import time
from Fyers_Tradebook import *
from Fyers_Utility import *
from Fyers_New_Recreate_Strategy_0 import new_recreate_strategy
from Fyers_Generic_Squareoff import squareoff_all_pending_positions
from Fyers_Utility import initialize_fyers
from logger_config import get_logger, setup_logger
from Fyers_PlaceOrder import place_order

#Get logger
#logger = setup_logger('main_logger', 'main.log')
logger = get_logger('main_logger')


def get_pending_positions(fyers):
    """
    Fetch all pending open positions from Fyers API and segregate them into CE, PE as well as Buy and Short side.
    :return: Dictionary containing segregated positions with only symbol, ltp, and side.
    """
    try:
        response = fyers.positions()
        if response.get("s") == "ok":
                positions = response.get("netPositions", [])
                #print(positions)
        
        # Check if the response is valid
        if positions:
            # Segregation dictionaries
            ce_buy = {}
            ce_short = {}
            pe_buy = {}
            pe_short = {}

            for position in positions:

                # Assuming 'quantity' is a key that indicates if the position is open
                if position.get("qty", 0) > 0 and position.get("productType") == "MARGIN": 

                    symbol = position["symbol"]
                    ltp = position["ltp"]
                    side = "BUY" if position["side"] ==1 else "SHORT"
                    qty = position["qty"]
                    PNL ='tatal_pnl'

                    # Classify as CE or PE and Buy or Short
                    if "CE" in symbol:
                        if side == "BUY":
                            ce_buy = {"symbol": symbol, "ltp": ltp, "side": side, "qty": qty}
                        else:
                            ce_short = {"symbol": symbol, "ltp": ltp, "side": side, "qty": qty}
                    elif "PE" in symbol:
                        if side == "BUY":
                            pe_buy = {"symbol": symbol, "ltp": ltp, "side": side, "qty": qty}
                        else:
                            pe_short = {"symbol": symbol, "ltp": ltp, "side": side, "qty": qty}

              # Print the segregated positions
            logger.info(f"CE_BUY:{ce_buy}")
            logger.info(f"CE_SHORT:{ce_short}")
            logger.info(f"PE_BUY:{pe_buy}")
            logger.info(f"PE_SHORT:{pe_short}")

            open_positions = [pos for pos in positions if pos.get("qty", 0) > 0]

            if len(open_positions) < 4:
                squareoff_all_pending_positions(fyers, open_positions)
            
    except Exception as e:
        return {"error": str(e)}

        
#---------------------------------------------------------------------------------------

fyers = initialize_fyers()

#Example Usage    
positions = get_pending_positions(fyers)