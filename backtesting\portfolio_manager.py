"""
Portfolio Manager Mo<PERSON>le
Handles position tracking, PnL calculation, and risk management for backtesting
"""

import pandas as pd
import numpy as np
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import logging
from dataclasses import dataclass, field
from .config import BacktestConfig

logger = logging.getLogger(__name__)


@dataclass
class Position:
    """Represents a trading position"""
    symbol: str
    side: str  # 'buy' or 'sell'
    quantity: int
    entry_price: float
    entry_time: datetime
    current_price: float = 0.0
    unrealized_pnl: float = 0.0
    commission: float = 0.0
    
    def update_price(self, new_price: float):
        """Update current price and calculate unrealized PnL"""
        self.current_price = new_price
        if self.side.lower() == 'buy':
            self.unrealized_pnl = (new_price - self.entry_price) * self.quantity
        else:  # sell
            self.unrealized_pnl = (self.entry_price - new_price) * self.quantity
    
    def get_market_value(self) -> float:
        """Get current market value of the position"""
        return self.current_price * self.quantity
    
    def to_dict(self) -> Dict:
        """Convert position to dictionary"""
        return {
            'symbol': self.symbol,
            'side': self.side,
            'quantity': self.quantity,
            'entry_price': self.entry_price,
            'entry_time': self.entry_time,
            'current_price': self.current_price,
            'unrealized_pnl': self.unrealized_pnl,
            'market_value': self.get_market_value(),
            'commission': self.commission
        }


@dataclass
class Trade:
    """Represents a completed trade"""
    symbol: str
    side: str
    quantity: int
    entry_price: float
    exit_price: float
    entry_time: datetime
    exit_time: datetime
    realized_pnl: float
    commission: float
    trade_type: str = "unknown"  # e.g., "initial", "delta_adjustment", "square_off"
    
    def to_dict(self) -> Dict:
        """Convert trade to dictionary"""
        return {
            'symbol': self.symbol,
            'side': self.side,
            'quantity': self.quantity,
            'entry_price': self.entry_price,
            'exit_price': self.exit_price,
            'entry_time': self.entry_time,
            'exit_time': self.exit_time,
            'realized_pnl': self.realized_pnl,
            'commission': self.commission,
            'trade_type': self.trade_type,
            'duration_minutes': (self.exit_time - self.entry_time).total_seconds() / 60
        }


class PortfolioManager:
    """Manages portfolio positions, trades, and PnL calculation"""
    
    def __init__(self, config: BacktestConfig, initial_capital: float = None):
        self.config = config
        self.initial_capital = initial_capital or config.initial_capital
        self.current_capital = self.initial_capital
        
        # Position tracking
        self.positions: Dict[str, Position] = {}
        self.completed_trades: List[Trade] = []
        
        # PnL tracking
        self.daily_pnl = 0.0
        self.total_realized_pnl = 0.0
        self.total_unrealized_pnl = 0.0
        self.total_commission = 0.0
        
        # Strategy specific positions
        self.strategy_positions = {
            'CE_SHORT': None,
            'PE_SHORT': None,
            'CE_BUY': None,
            'PE_BUY': None
        }
        
        # Risk tracking
        self.max_drawdown = 0.0
        self.peak_capital = self.initial_capital
        
    def add_position(self, symbol: str, side: str, quantity: int, 
                    entry_price: float, entry_time: datetime, 
                    commission: float = 0.0, position_type: str = None) -> bool:
        """Add a new position to the portfolio"""
        try:
            if symbol in self.positions:
                logger.warning(f"Position for {symbol} already exists. Updating...")
                self.close_position(symbol, entry_price, entry_time, "position_update")
            
            position = Position(
                symbol=symbol,
                side=side,
                quantity=quantity,
                entry_price=entry_price,
                entry_time=entry_time,
                current_price=entry_price,
                commission=commission
            )
            
            self.positions[symbol] = position
            self.total_commission += commission
            
            # Update strategy positions if position type is specified
            if position_type and position_type in self.strategy_positions:
                self.strategy_positions[position_type] = position
            
            logger.info(f"Added position: {symbol} {side} {quantity} @ {entry_price}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding position for {symbol}: {e}")
            return False
    
    def close_position(self, symbol: str, exit_price: float, exit_time: datetime, 
                      trade_type: str = "manual_close") -> Optional[Trade]:
        """Close a position and record the trade"""
        if symbol not in self.positions:
            logger.warning(f"No position found for {symbol}")
            return None
        
        try:
            position = self.positions[symbol]
            
            # Calculate realized PnL
            if position.side.lower() == 'buy':
                realized_pnl = (exit_price - position.entry_price) * position.quantity
            else:  # sell
                realized_pnl = (position.entry_price - exit_price) * position.quantity
            
            # Account for commission
            total_commission = position.commission + self.config.commission_per_trade
            realized_pnl -= total_commission
            
            # Create trade record
            trade = Trade(
                symbol=symbol,
                side=position.side,
                quantity=position.quantity,
                entry_price=position.entry_price,
                exit_price=exit_price,
                entry_time=position.entry_time,
                exit_time=exit_time,
                realized_pnl=realized_pnl,
                commission=total_commission,
                trade_type=trade_type
            )
            
            self.completed_trades.append(trade)
            self.total_realized_pnl += realized_pnl
            self.total_commission += self.config.commission_per_trade
            
            # Remove from positions
            del self.positions[symbol]
            
            # Remove from strategy positions if present
            for pos_type, pos in self.strategy_positions.items():
                if pos and pos.symbol == symbol:
                    self.strategy_positions[pos_type] = None
                    break
            
            logger.info(f"Closed position: {symbol} PnL: {realized_pnl:.2f}")
            return trade
            
        except Exception as e:
            logger.error(f"Error closing position for {symbol}: {e}")
            return None
    
    def update_positions(self, market_data: Dict[str, Dict]):
        """Update all positions with current market prices"""
        self.total_unrealized_pnl = 0.0
        
        for symbol, position in self.positions.items():
            if symbol in market_data:
                new_price = market_data[symbol].get('ltp', position.current_price)
                position.update_price(new_price)
                self.total_unrealized_pnl += position.unrealized_pnl
    
    def get_total_pnl(self) -> float:
        """Get total PnL (realized + unrealized)"""
        return self.total_realized_pnl + self.total_unrealized_pnl
    
    def get_current_capital(self) -> float:
        """Get current capital including PnL"""
        return self.initial_capital + self.get_total_pnl()
    
    def get_strategy_positions(self) -> Dict[str, Optional[Dict]]:
        """Get current strategy positions in the format expected by the strategy"""
        result = {}
        
        for pos_type, position in self.strategy_positions.items():
            if position:
                result[pos_type] = {
                    'symbol': position.symbol,
                    'ltp': position.current_price,
                    'qty': position.quantity,
                    'side': position.side
                }
            else:
                result[pos_type] = None
        
        return result
    
    def square_off_all_positions(self, exit_time: datetime, market_data: Dict[str, Dict]):
        """Square off all open positions"""
        positions_to_close = list(self.positions.keys())
        
        for symbol in positions_to_close:
            if symbol in market_data:
                exit_price = market_data[symbol].get('ltp', self.positions[symbol].current_price)
            else:
                exit_price = self.positions[symbol].current_price
                logger.warning(f"No market data for {symbol}, using last known price")
            
            self.close_position(symbol, exit_price, exit_time, "square_off")
    
    def check_risk_limits(self) -> Dict[str, bool]:
        """Check if any risk limits are breached"""
        current_pnl = self.get_total_pnl()
        current_capital = self.get_current_capital()
        
        # Update peak capital and drawdown
        if current_capital > self.peak_capital:
            self.peak_capital = current_capital
        
        current_drawdown = (self.peak_capital - current_capital) / self.peak_capital
        if current_drawdown > self.max_drawdown:
            self.max_drawdown = current_drawdown
        
        return {
            'max_loss_breached': current_pnl <= -self.config.max_loss_per_day,
            'max_profit_reached': current_pnl >= self.config.max_profit_per_day,
            'drawdown_limit': current_drawdown > 0.2  # 20% drawdown limit
        }
    
    def get_portfolio_summary(self) -> Dict:
        """Get comprehensive portfolio summary"""
        total_pnl = self.get_total_pnl()
        current_capital = self.get_current_capital()
        
        return {
            'initial_capital': self.initial_capital,
            'current_capital': current_capital,
            'total_pnl': total_pnl,
            'realized_pnl': self.total_realized_pnl,
            'unrealized_pnl': self.total_unrealized_pnl,
            'total_commission': self.total_commission,
            'open_positions': len(self.positions),
            'completed_trades': len(self.completed_trades),
            'max_drawdown': self.max_drawdown,
            'return_pct': (total_pnl / self.initial_capital) * 100,
            'positions': [pos.to_dict() for pos in self.positions.values()],
            'strategy_positions': self.get_strategy_positions()
        }
    
    def reset_daily_pnl(self):
        """Reset daily PnL tracking for new trading day"""
        self.daily_pnl = 0.0
        self.total_realized_pnl = 0.0
        self.total_unrealized_pnl = 0.0
