
def get_exchangeInstrumentID(option, masterdf):
    symbol_without_nse = option['symbol'].replace("NSE:", "")  # Removes "NSE:" prefix

    matched_row = masterdf[(masterdf['Description'] == symbol_without_nse)] # Match Description without the "NSE:" prefix
              
    if not matched_row.empty:
        return int(matched_row['ExchangeInstrumentID'].values[0])  # Return the first matched ExchangeInstrumentID
    else:
        return None