import logger_config
from dotenv import load_dotenv
import os

load_dotenv()

#Get logger
logger = logger_config.get_logger('main_logger')

def get_threshold(vix, fixed_threshold=None):
    """
    Calculate the threshold based on VIX value.

    VIX,Threshold - (10,0.3), (11,0.32), (12,0.34), (13,0.36), (14,0.38), (15,0.4), (16,0.41), (17,0.41), (18,0.42), (19,0.42), (20,0.43), (21,0.43), (22,0.44)

    """
    if(fixed_threshold is not None):
        threshold = fixed_threshold
        logger.info(f"Fixed Threshold:{threshold}")
        return round(threshold, 2)
    else :
        if(vix<=9):
            threshold = 0.28
        elif(vix>9 and vix<23):
            threshold = 0.3 + 0.02 * (vix - 10)
        else:
            threshold = 0.45
        Diff = float(os.getenv("Diff"))
        logger.info(f"Diff:{Diff}")    
        threshold = threshold - Diff
        logger.info(f"Threshold after diff:{threshold}")
    return round(threshold, 2)

def get_vix(fyers):
    """
    Fetch current VIX value from Fyers API.
    """
    try:
        response = fyers.quotes({"symbols": "NSE:INDIAVIX-INDEX"})
        #print("API Response:", response)  # Debug: Print the full response to see its structure

        # Check if the response status is "ok"
        if response.get("s") == "ok":
            # Check if the "d" field exists and contains data
            if response["d"]:
                # Access the "lp" field inside the "v" sub-field
                vix_value = response["d"][0]["v"].get("lp")
                if vix_value is not None:
                    return vix_value
                else:
                    logger.info("Error: 'lp' field is missing in the response.")
                    return None
            else:
                logger.info("No data available in 'd' field.")
                return None
        else:
            logger.info(f"Error in response:{response.get("message", "Unknown error")}")
            return None
    except Exception as e:
        logger.error(f"Error fetching VIX value: {e}")  # Print the error if there is one
        return None

