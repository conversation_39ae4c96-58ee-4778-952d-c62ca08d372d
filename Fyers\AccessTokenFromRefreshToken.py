import hashlib
import requests
import os
from dotenv import load_dotenv, set_key, find_dotenv
import logger_config 
from datetime import datetime

#Get logger
logger = logger_config.get_logger('main_logger')
#logger = logger_config.setup_logger('main_logger', 'main.log')

def get_access_token():
    # Load environment variables from .env file
    load_dotenv()

    # Step 1: Define App ID and Secret Key
    app_id = os.getenv("APP_ID")
    app_secret = os.getenv("APP_SECRET")
    refresh_token = os.getenv("REFRESH_TOKEN")
    pin = os.getenv("PIN")

    # Ensure environment variables are not None
    if not all([app_id, app_secret, refresh_token, pin]):
        logger.error("One or more required environment variables are missing!")
        raise ValueError("One or more required environment variables are missing!")

    # Step 2: Generate SHA-256 Hash
    concatenated_string = app_id + ":" + app_secret
    app_id_hash = hashlib.sha256(concatenated_string.encode()).hexdigest()
    logger.debug(f"Generated App ID Hash: {app_id_hash}")

    # Step 3: Define URL, headers, and payload
    url = "https://api-t1.fyers.in/api/v3/validate-refresh-token"
    headers = {
        "Content-Type": "application/json"
    }
    payload = {
        "grant_type": "refresh_token",
        "appIdHash": app_id_hash,
        "refresh_token": refresh_token,
        "pin": pin
    }

    # Step 4: Send POST request
    response = requests.post(url, headers=headers, json=payload)
      
    # Step 5: Print the response
    if response.status_code == 200:
        # Extract the access token from the response
        new_access_token = response.json().get("access_token")
        logger.info(f"Updated Access Token as on {datetime.now()}:, {new_access_token}")

        if new_access_token:
            def update_access_token(access_token):
                 # Locate the .env file
                env_path = find_dotenv()
                if not env_path:
                    logger.error("Error: .env file not found.")
                    return

                # Load existing environment variables from .env file
                load_dotenv(env_path)
                
                # Check if the ACCESS_TOKEN already exists
                existing_token = os.getenv('ACCESS_TOKEN')
                
                if existing_token:
                    logger.info("Existing access token found in environment file. Updating it.")
                else:
                    logger.info("No existing access token found. Creating a new entry.")

                # Update the ACCESS_TOKEN and LAST_TOKEN_UPDATE in the .env file
                today = datetime.now().strftime('%Y-%m-%d')
                set_key(env_path, 'ACCESS_TOKEN', access_token)
                set_key(env_path, 'LAST_TOKEN_UPDATE', today)
                logger.info("Access token and last update date stored successfully.")


            update_access_token(new_access_token)
        else:
            logger.error("Error: Access token not found in response.")
    else:
        logger.error(f"Failed to fetch access token. Status Code: {response.status_code}, Response: {response.text}")

# Example usage
#if __name__ == "__main__":
    #get_access_token()
