from Fyers_PlaceOrder import place_order
from Fyers_Get_OTM_Options import get_nifty_otm_options
from Fyers_VIX_Threshold import *
from Fyers_Utility import *
from logger_config import get_logger

# Get logger
logger = get_logger('main_logger')

# Example Usage:
symbols_list = [
    {"option":{"symbol":"NSE:NIFTY2522022950PE", "price":"0.05"},
     "side":"buy",
     "qty":GLOBAL_QTY,
    },
    {"option":{"symbol":"NSE:NIFTY2522023800CE", "price":"0.05"},
     "side":"buy",
     "qty":GLOBAL_QTY,
    }, 
    {"option":{"symbol":"NSE:NIFTY2521323200PE" , "price":"0.05"},
     "side":"sell",
     "qty":GLOBAL_QTY,
    },
    {"option":{"symbol":"NSE:NIFTY2521323600CE", "price":"0.05"},
     "side":"sell",
     "qty":GLOBAL_QTY,
    },
       
    ]  # List of symbols to trade


#------------------------------------------------------------------------------------------------------------------------
fyers = initialize_fyers()

logger.info("Executing order placement for symbols: %s", symbols_list)
#result = place_selected_symbol_orders(fyers, symbols_list, place_buy_ce=False, place_buy_pe=False, place_sell_ce=False, place_sell_pe=True)
for symbol in symbols_list:
    option = symbol["option"]
    side = symbol["side"]
    qty = symbol["qty"]
    order_type = "limit"
    place_order(fyers, option, side, qty, order_type)
    
logger.info("Order placement completed.")
