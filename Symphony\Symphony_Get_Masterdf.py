from io import StringIO
import pandas as pd
import os
from datetime import datetime
from Symphony import Symphony_Utility 

# Global variable to store masterdf in memory
_cached_masterdf = None
_cache_date = None

"""Get Master Instruments Request"""
def get_masterdf(market_symphony):
    global _cached_masterdf, _cache_date
    today = datetime.now().date()
    
    # Return cached dataframe if it exists and is from today
    if _cached_masterdf is not None and _cache_date == today:
        return _cached_masterdf

    # Generate new data if cache doesn't exist or is outdated
    exchangesegments = [market_symphony.EXCHANGE_NSEFO]
    response = market_symphony.get_master(exchangeSegmentList=exchangesegments)

    masterdf = pd.read_csv(StringIO(response['result']), sep='|', usecols=range(19), header=None, low_memory=False)
    masterdf.columns = ["ExchangeSegment", 'ExchangeInstrumentID', "InstrumentType", "Name", "Description", "Series","NameWithSeries", "InstrumentID", "PriceBand.High", "PriceBand.Low", "Freeze<PERSON><PERSON>", "TickSize", "LotSize", "Multiplier", "UnderlyingInstrumentID", "UnderlyingIndexName","ContractExpiration", "StrikePrice", "OptionType"]
    masterdf['ContractExpiration']=pd.to_datetime(masterdf['ContractExpiration']).apply(lambda x:x.date())
    
     # Update the cache
    _cached_masterdf = masterdf
    _cache_date = today

    return masterdf

#--------------------------------------------------------------
#market_symphony = initialize_market_symphony()
#df=get_masterdf(market_symphony)
