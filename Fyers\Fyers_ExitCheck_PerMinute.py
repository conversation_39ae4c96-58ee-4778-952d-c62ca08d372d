from Fyers_Exit_TP_SL import exit_check as exit_check
from Fyers_Get_TargetProfit_StopLoss import get_targetprofit_stoploss
from Fyers.Fyers_save_final_pnl import save_final_pnl
from Fyers_Generic_Squareoff import squareoff_positions
import time

def perform_exit_check(fyers, positions, logger):
    """
    This function checks if the exit conditions (TargetProfit or StopLoss)
    are met. If they are, it squares off the positions and exits the script.
    It runs the exit check every minute.
    """
    #targetprofit, stoploss = get_targetprofit_stoploss()  # Get the target profit and stop loss values
    targetprofit = 1500
    stoploss= -6000
    #logger.debug(f"Target Profit: {targetprofit}, Stop Loss: {stoploss}")
    
    # Call the exit check function to see if exit conditions are met
    exit_check_result = exit_check(fyers, targetprofit, stoploss)
    if exit_check_result:
        logger.info("TargetProfit/StopLoss exit conditions met. Proceeding to square off positions.")
        
        squareoff_positions(fyers, positions)  # Square off positions
        
        time.sleep(0.05)  # Small delay before saving final PNL
        save_final_pnl(fyers)  # Save the final PNL after squaring off positions
        return True  # Return True if exit conditions are met
    else:
        logger.info("TargetProfit/StopLoss exit conditions not met. Continuing to monitor.")
        return False  # Return False if exit conditions are not met

